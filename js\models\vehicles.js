/**
 * 车辆系统 - 负责创建和管理城市中的车辆
 */
class VehicleSystem {
    constructor(scene, roadSystem) {
        this.scene = scene;
        this.roadSystem = roadSystem;
        this.vehicles = [];
        this.materials = this.createMaterials();
        this.currentLane = 0;  // 当前车道
        this.spawnInterval = 2; // 车辆生成间隔
        this.spawnTimer = 0;    // 生成计时器
    }
    
    createMaterials() {
        // 车身颜色
        const carColors = [
            0xFF0000, // 红色
            0x0000FF, // 蓝色
            0x00FF00, // 绿色
            0xFFFF00, // 黄色
            0x000000, // 黑色
            0xFFFFFF, // 白色
            0x808080, // 灰色
            0xFFA500, // 橙色
            0x800080, // 紫色
            0x008080  // 青色
        ];
        
        const materials = {
            carBodyMaterials: [],
            glass: new THREE.MeshPhongMaterial({
                color: 0x111111,
                specular: 0x555555,
                shininess: 100,
                transparent: true,
                opacity: 0.7
            }),
            wheel: new THREE.MeshPhongMaterial({
                color: 0x333333,
                specular: 0x222222,
                shininess: 30
            }),
            wheelRim: new THREE.MeshPhongMaterial({
                color: 0x888888,
                specular: 0xFFFFFF,
                shininess: 80
            }),
            light: new THREE.MeshPhongMaterial({
                color: 0xFFFFFF,
                specular: 0xFFFFFF,
                shininess: 100,
                transparent: true,
                opacity: 0.9
            }),
            backLight: new THREE.MeshPhongMaterial({
                color: 0xFF0000,
                specular: 0xFF5555,
                shininess: 100,
                transparent: true,
                opacity: 0.8
            })
        };
        
        // 创建不同颜色的车身材质
        carColors.forEach(color => {
            materials.carBodyMaterials.push(
                new THREE.MeshPhongMaterial({
                    color: color,
                    specular: 0xFFFFFF,
                    shininess: 80
                })
            );
        });
        
        return materials;
    }
    
    // 生成初始车辆
    generateVehicles(count = 20) {
        // 清除现有车辆
        this.clearVehicles();
        
        // 确保道路系统已初始化
        if (!this.roadSystem || !this.roadSystem.roads || this.roadSystem.roads.length === 0) {
            console.error("无法生成车辆：道路系统未初始化");
            return;
        }
        
        // 生成新车辆
        for (let i = 0; i < count; i++) {
            this.spawnVehicle();
        }
    }
    
    // 清除现有车辆
    clearVehicles() {
        for (const vehicle of this.vehicles) {
            this.scene.remove(vehicle.mesh);
        }
        this.vehicles = [];
    }
    
    // 生成单个车辆
    spawnVehicle() {
        // 选择道路
        const road = this.getRandomRoad();
        if (!road) return;
        
        // 随机选择车辆类型
        const vehicleType = Math.random() < 0.8 ? 'car' : 'van';
        
        // 创建车辆对象
        const vehicle = this.createVehicle(vehicleType);
        if (!vehicle) return;
        
        // 放置车辆在道路上
        const roadBbox = new THREE.Box3().setFromObject(road);
        const roadLength = Math.max(roadBbox.max.x - roadBbox.min.x, roadBbox.max.z - roadBbox.min.z);
        
        // 判断道路方向
        const isHorizontal = (roadBbox.max.x - roadBbox.min.x) > (roadBbox.max.z - roadBbox.min.z);
        
        // 为道路角度计算正弦和余弦
        const angle = road.rotation.z;
        const sinAngle = Math.sin(angle);
        const cosAngle = Math.cos(angle);
        
        // 计算道路起点和方向
        let startX, startZ, dirX, dirZ;
        if (isHorizontal) {
            startX = roadBbox.min.x;
            startZ = road.position.z;
            dirX = 1;
            dirZ = 0;
        } else {
            startX = road.position.x;
            startZ = roadBbox.min.z;
            dirX = 0;
            dirZ = 1;
        }
        
        // 随机位置
        const distance = Math.random() * roadLength;
        const posX = startX + dirX * distance * cosAngle - dirZ * distance * sinAngle;
        const posZ = startZ + dirX * distance * sinAngle + dirZ * distance * cosAngle;
        
        // 设置车辆位置和旋转
        vehicle.mesh.position.set(posX, 0, posZ);
        
        // 根据车道设置偏移，并正确旋转车辆
        const laneOffset = 3; // 车道宽度
        if (isHorizontal) {
            vehicle.mesh.rotation.y = -angle;
            // 根据车道设置Z轴偏移
            vehicle.mesh.position.z += (vehicle.lane === 0 ? -laneOffset : laneOffset);
            // 根据车道设置车辆方向
            vehicle.direction = vehicle.lane === 0 ? 1 : -1;
        } else {
            vehicle.mesh.rotation.y = Math.PI/2 - angle;
            // 根据车道设置X轴偏移
            vehicle.mesh.position.x += (vehicle.lane === 0 ? -laneOffset : laneOffset);
            // 根据车道设置车辆方向
            vehicle.direction = vehicle.lane === 0 ? 1 : -1;
        }
        
        // 设置车辆速度
        vehicle.speed = 5 + Math.random() * 5; // 速度范围
        
        // 保存道路信息
        vehicle.road = road;
        vehicle.isHorizontal = isHorizontal;
        
        // 添加到场景和车辆列表
        this.scene.add(vehicle.mesh);
        this.vehicles.push(vehicle);
    }
    
    // 获取随机道路
    getRandomRoad() {
        if (!this.roadSystem.roads || this.roadSystem.roads.length === 0) {
            return null;
        }
        return this.roadSystem.roads[Math.floor(Math.random() * this.roadSystem.roads.length)];
    }
    
    // 创建车辆模型
    createVehicle(type) {
        // 决定车道
        const lane = this.currentLane;
        this.currentLane = (this.currentLane + 1) % 2; // 交替车道
        
        switch (type) {
            case 'car':
                return this.createCar(lane);
            case 'van':
                return this.createVan(lane);
            default:
                return this.createCar(lane);
        }
    }
    
    // 创建轿车
    createCar(lane) {
        const carGroup = new THREE.Group();
        
        // 随机选择车身颜色
        const bodyMaterial = this.materials.carBodyMaterials[
            Math.floor(Math.random() * this.materials.carBodyMaterials.length)
        ];
        
        // 车身尺寸
        const width = 2.0;
        const length = 4.5;
        const height = 1.5;
        
        // 车身底部
        const bodyLowerGeometry = new THREE.BoxGeometry(width, height * 0.5, length);
        const bodyLower = new THREE.Mesh(bodyLowerGeometry, bodyMaterial);
        bodyLower.position.y = height * 0.25;
        bodyLower.castShadow = true;
        bodyLower.receiveShadow = true;
        carGroup.add(bodyLower);
        
        // 车身上部
        const bodyUpperWidth = width * 0.8;
        const bodyUpperLength = length * 0.6;
        const bodyUpperGeometry = new THREE.BoxGeometry(bodyUpperWidth, height * 0.5, bodyUpperLength);
        const bodyUpper = new THREE.Mesh(bodyUpperGeometry, bodyMaterial);
        bodyUpper.position.set(0, height * 0.75, length * 0.05);
        bodyUpper.castShadow = true;
        bodyUpper.receiveShadow = true;
        carGroup.add(bodyUpper);
        
        // 挡风玻璃
        const windshieldGeometry = new THREE.PlaneGeometry(bodyUpperWidth * 0.9, height * 0.4);
        const windshield = new THREE.Mesh(windshieldGeometry, this.materials.glass);
        windshield.position.set(0, height * 0.8, length * 0.3);
        windshield.rotation.x = -Math.PI / 6;
        carGroup.add(windshield);
        
        // 后窗
        const rearWindowGeometry = new THREE.PlaneGeometry(bodyUpperWidth * 0.9, height * 0.4);
        const rearWindow = new THREE.Mesh(rearWindowGeometry, this.materials.glass);
        rearWindow.position.set(0, height * 0.8, -length * 0.2);
        rearWindow.rotation.x = Math.PI / 6;
        rearWindow.rotation.y = Math.PI;
        carGroup.add(rearWindow);
        
        // 车轮
        this.addWheels(carGroup, width, length, 0.4);
        
        // 车灯（前）
        const frontLightGeometry = new THREE.BoxGeometry(0.4, 0.2, 0.1);
        const frontLightLeft = new THREE.Mesh(frontLightGeometry, this.materials.light);
        frontLightLeft.position.set(width * 0.4, height * 0.2, length * 0.5);
        carGroup.add(frontLightLeft);
        
        const frontLightRight = new THREE.Mesh(frontLightGeometry, this.materials.light);
        frontLightRight.position.set(width * -0.4, height * 0.2, length * 0.5);
        carGroup.add(frontLightRight);
        
        // 车灯（后）
        const backLightGeometry = new THREE.BoxGeometry(0.4, 0.2, 0.1);
        const backLightLeft = new THREE.Mesh(backLightGeometry, this.materials.backLight);
        backLightLeft.position.set(width * 0.4, height * 0.2, -length * 0.5);
        carGroup.add(backLightLeft);
        
        const backLightRight = new THREE.Mesh(backLightGeometry, this.materials.backLight);
        backLightRight.position.set(width * -0.4, height * 0.2, -length * 0.5);
        carGroup.add(backLightRight);
        
        // 调整整体位置
        carGroup.position.y = 0.4; // 车轮半径
        
        return {
            mesh: carGroup,
            type: 'car',
            lane: lane,
            speed: 0,
            direction: 0,
            road: null,
            isHorizontal: true
        };
    }
    
    // 创建货车
    createVan(lane) {
        const vanGroup = new THREE.Group();
        
        // 随机选择车身颜色
        const bodyMaterial = this.materials.carBodyMaterials[
            Math.floor(Math.random() * this.materials.carBodyMaterials.length)
        ];
        
        // 车身尺寸
        const width = 2.5;
        const length = 6.0;
        const height = 2.8;
        
        // 驾驶室
        const cabinWidth = width;
        const cabinLength = length * 0.3;
        const cabinGeometry = new THREE.BoxGeometry(cabinWidth, height, cabinLength);
        const cabin = new THREE.Mesh(cabinGeometry, bodyMaterial);
        cabin.position.set(0, height * 0.5, length * 0.35);
        cabin.castShadow = true;
        cabin.receiveShadow = true;
        vanGroup.add(cabin);
        
        // 货箱
        const cargoWidth = width;
        const cargoLength = length * 0.7;
        const cargoHeight = height * 1.1;
        const cargoGeometry = new THREE.BoxGeometry(cargoWidth, cargoHeight, cargoLength);
        const cargo = new THREE.Mesh(cargoGeometry, bodyMaterial);
        cargo.position.set(0, cargoHeight * 0.5, -length * 0.15);
        cargo.castShadow = true;
        cargo.receiveShadow = true;
        vanGroup.add(cargo);
        
        // 挡风玻璃
        const windshieldGeometry = new THREE.PlaneGeometry(cabinWidth * 0.8, height * 0.4);
        const windshield = new THREE.Mesh(windshieldGeometry, this.materials.glass);
        windshield.position.set(0, height * 0.8, length * 0.45);
        windshield.rotation.x = -Math.PI / 8;
        vanGroup.add(windshield);
        
        // 车轮
        this.addWheels(vanGroup, width, length, 0.5);
        
        // 车灯（前）
        const frontLightGeometry = new THREE.BoxGeometry(0.5, 0.3, 0.1);
        const frontLightLeft = new THREE.Mesh(frontLightGeometry, this.materials.light);
        frontLightLeft.position.set(width * 0.4, height * 0.3, length * 0.5);
        vanGroup.add(frontLightLeft);
        
        const frontLightRight = new THREE.Mesh(frontLightGeometry, this.materials.light);
        frontLightRight.position.set(width * -0.4, height * 0.3, length * 0.5);
        vanGroup.add(frontLightRight);
        
        // 车灯（后）
        const backLightGeometry = new THREE.BoxGeometry(0.5, 0.3, 0.1);
        const backLightLeft = new THREE.Mesh(backLightGeometry, this.materials.backLight);
        backLightLeft.position.set(width * 0.4, height * 0.3, -length * 0.5);
        vanGroup.add(backLightLeft);
        
        const backLightRight = new THREE.Mesh(backLightGeometry, this.materials.backLight);
        backLightRight.position.set(width * -0.4, height * 0.3, -length * 0.5);
        vanGroup.add(backLightRight);
        
        // 调整整体位置
        vanGroup.position.y = 0.5; // 车轮半径
        
        return {
            mesh: vanGroup,
            type: 'van',
            lane: lane,
            speed: 0,
            direction: 0,
            road: null,
            isHorizontal: true
        };
    }
    
    // 添加车轮
    addWheels(vehicleGroup, width, length, wheelRadius) {
        const wheelGeometry = new THREE.CylinderGeometry(wheelRadius, wheelRadius, 0.3, 12);
        wheelGeometry.rotateX(Math.PI / 2);
        
        // 车轮位置
        const positions = [
            // 左前
            {x: width * 0.5, y: wheelRadius, z: length * 0.3},
            // 右前
            {x: -width * 0.5, y: wheelRadius, z: length * 0.3},
            // 左后
            {x: width * 0.5, y: wheelRadius, z: -length * 0.3},
            // 右后
            {x: -width * 0.5, y: wheelRadius, z: -length * 0.3}
        ];
        
        for (const pos of positions) {
            const wheel = new THREE.Mesh(wheelGeometry, this.materials.wheel);
            wheel.position.set(pos.x, pos.y, pos.z);
            wheel.castShadow = true;
            vehicleGroup.add(wheel);
            
            // 轮毂
            const hubGeometry = new THREE.CylinderGeometry(wheelRadius * 0.4, wheelRadius * 0.4, 0.31, 8);
            hubGeometry.rotateX(Math.PI / 2);
            const hub = new THREE.Mesh(hubGeometry, this.materials.wheelRim);
            hub.position.copy(wheel.position);
            vehicleGroup.add(hub);
        }
    }
    
    // 更新车辆位置
    update(deltaTime) {
        // 定期生成新车辆
        this.spawnTimer += deltaTime;
        if (this.spawnTimer >= this.spawnInterval) {
            this.spawnTimer = 0;
            if (this.vehicles.length < 40) { // 限制最大车辆数
                this.spawnVehicle();
            }
        }
        
        // 更新现有车辆
        for (let i = this.vehicles.length - 1; i >= 0; i--) {
            const vehicle = this.vehicles[i];
            
            // 计算移动距离
            const moveDistance = vehicle.speed * deltaTime;
            
            // 根据道路方向和车辆方向移动车辆
            if (vehicle.isHorizontal) {
                vehicle.mesh.position.x += moveDistance * vehicle.direction;
            } else {
                vehicle.mesh.position.z += moveDistance * vehicle.direction;
            }
            
            // 检查是否到达道路尽头
            const roadBbox = new THREE.Box3().setFromObject(vehicle.road);
            let removeVehicle = false;
            
            if (vehicle.isHorizontal) {
                if ((vehicle.direction > 0 && vehicle.mesh.position.x > roadBbox.max.x + 10) ||
                    (vehicle.direction < 0 && vehicle.mesh.position.x < roadBbox.min.x - 10)) {
                    removeVehicle = true;
                }
            } else {
                if ((vehicle.direction > 0 && vehicle.mesh.position.z > roadBbox.max.z + 10) ||
                    (vehicle.direction < 0 && vehicle.mesh.position.z < roadBbox.min.z - 10)) {
                    removeVehicle = true;
                }
            }
            
            // 移除超出范围的车辆
            if (removeVehicle) {
                this.scene.remove(vehicle.mesh);
                this.vehicles.splice(i, 1);
            }
        }
    }
} 