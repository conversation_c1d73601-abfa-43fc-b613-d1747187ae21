{"enabled": true, "name": "代码错误检查修复器", "description": "监控代码文件变化，自动检查语法错误、逻辑错误和潜在问题，并提供修复建议或自动修复", "version": "1", "when": {"type": "fileEdited", "patterns": ["*.py", "*.js", "*.html", "*.css", "*.json", "*.yml", "*.yaml", "*.md", "*.txt"]}, "then": {"type": "askAgent", "prompt": "请检查刚刚修改的代码文件中是否存在以下问题：\n1. 语法错误（如缩进错误、括号不匹配、拼写错误等）\n2. 逻辑错误（如变量未定义、函数调用错误、导入错误等）\n3. 潜在的运行时错误（如空值引用、类型错误等）\n4. 代码规范问题（如命名不规范、注释缺失等）\n5. 性能问题和安全隐患\n\n如果发现错误，请：\n- 详细说明错误类型和位置\n- 提供具体的修复方案\n- 如果可能，直接提供修复后的代码\n- 解释为什么会出现这个错误以及如何避免\n\n请用中文回答，并确保修复方案简单易懂。"}}