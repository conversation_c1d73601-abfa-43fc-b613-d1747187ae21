/**
 * 工具函数库
 */

// 生成指定范围内的随机数
function randomFloat(min, max) {
    return Math.random() * (max - min) + min;
}

// 生成指定范围内的随机整数
function randomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 随机从数组中选择一个元素
function randomChoice(array) {
    return array[Math.floor(Math.random() * array.length)];
}

// 生成随机颜色
function randomColor() {
    return '#' + Math.floor(Math.random() * 0xffffff).toString(16).padStart(6, '0');
}

// 生成随机灰度颜色
function randomGrayColor(minBrightness = 0.3, maxBrightness = 0.9) {
    const brightness = randomFloat(minBrightness, maxBrightness);
    const value = Math.floor(brightness * 255);
    return `rgb(${value}, ${value}, ${value})`;
}

// 将时间格式化为HH:MM
function formatTime(hour) {
    const hourInt = Math.floor(hour);
    const minutes = Math.floor((hour - hourInt) * 60);
    return `${hourInt.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

// 角度转弧度
function degToRad(degrees) {
    return degrees * Math.PI / 180;
}

// 弧度转角度
function radToDeg(radians) {
    return radians * 180 / Math.PI;
}

// 在两点之间创建贝塞尔曲线
function createBezierCurve(startPoint, endPoint, curveHeight = 0.5, segments = 20) {
    const points = [];
    const distance = Math.sqrt(
        Math.pow(endPoint.x - startPoint.x, 2) + 
        Math.pow(endPoint.y - startPoint.y, 2) + 
        Math.pow(endPoint.z - startPoint.z, 2)
    );
    
    const midPoint = {
        x: (startPoint.x + endPoint.x) / 2,
        y: (startPoint.y + endPoint.y) / 2 + distance * curveHeight,
        z: (startPoint.z + endPoint.z) / 2
    };
    
    for (let i = 0; i <= segments; i++) {
        const t = i / segments;
        
        // 二次贝塞尔曲线公式
        const x = Math.pow(1 - t, 2) * startPoint.x + 
                 2 * (1 - t) * t * midPoint.x + 
                 Math.pow(t, 2) * endPoint.x;
        
        const y = Math.pow(1 - t, 2) * startPoint.y + 
                 2 * (1 - t) * t * midPoint.y + 
                 Math.pow(t, 2) * endPoint.y;
        
        const z = Math.pow(1 - t, 2) * startPoint.z + 
                 2 * (1 - t) * t * midPoint.z + 
                 Math.pow(t, 2) * endPoint.z;
        
        points.push(new THREE.Vector3(x, y, z));
    }
    
    return points;
}

// 在指定范围内生成一组不重叠的点
function generateNonOverlappingPoints(count, minX, maxX, minZ, maxZ, minDistance) {
    const points = [];
    let attempts = 0;
    const maxAttempts = count * 10; // 最大尝试次数限制
    
    while (points.length < count && attempts < maxAttempts) {
        const x = randomFloat(minX, maxX);
        const z = randomFloat(minZ, maxZ);
        const newPoint = { x, z };
        
        let tooClose = false;
        for (const point of points) {
            const distance = Math.sqrt(Math.pow(point.x - x, 2) + Math.pow(point.z - z, 2));
            if (distance < minDistance) {
                tooClose = true;
                break;
            }
        }
        
        if (!tooClose) {
            points.push(newPoint);
        }
        
        attempts++;
    }
    
    return points;
}

// 帧率限制器
class FrameRateLimiter {
    constructor(targetFPS = 60) {
        this.targetFPS = targetFPS;
        this.frameTime = 1000 / targetFPS;
        this.lastFrameTime = 0;
    }
    
    shouldRender(timestamp) {
        if (timestamp - this.lastFrameTime >= this.frameTime) {
            this.lastFrameTime = timestamp;
            return true;
        }
        return false;
    }
}

// 对象池
class ObjectPool {
    constructor(createFunc, initialSize = 20) {
        this.createFunc = createFunc;
        this.pool = [];
        
        // 初始化对象池
        for (let i = 0; i < initialSize; i++) {
            this.pool.push(this.createFunc());
        }
    }
    
    get() {
        if (this.pool.length > 0) {
            return this.pool.pop();
        } else {
            return this.createFunc();
        }
    }
    
    release(object) {
        this.pool.push(object);
    }
} 