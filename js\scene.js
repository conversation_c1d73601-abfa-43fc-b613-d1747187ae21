/**
 * 城市场景类 - 负责初始化和管理整个3D城市场景
 */
class CityScene {
    constructor() {
        // 场景初始化
        this.initScene();
        this.initCamera();
        this.initRenderer();
        this.initLights();
        this.initGround();
        this.initControls();
        
        // 初始化环境系统（昼夜循环）
        this.environment = new Environment(this.scene);
        
        // 创建城市元素
        this.initCitySystems();
        
        // 用于计算帧率和更新时间的变量
        this.clock = new THREE.Clock();
        this.frameCount = 0;
        this.lastTime = 0;
        this.fps = 0;
        
        // 添加调试信息
        this.addDebugHelpers();
        
        // 启动动画循环
        this.animate();
        
        console.log("城市场景初始化完成");
    }
    
    // 初始化基础场景
    initScene() {
        console.log("初始化场景...");
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.FogExp2(0x87CEEB, 0.002); // 添加淡蓝色雾气效果
    }
    
    // 初始化摄像机
    initCamera() {
        console.log("初始化摄像机...");
        this.camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.camera.position.set(50, 50, 50);
        this.camera.lookAt(0, 0, 0);
    }
    
    // 初始化渲染器
    initRenderer() {
        console.log("初始化渲染器...");
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.body.appendChild(this.renderer.domElement);
        
        // 添加窗口大小变化的监听器
        window.addEventListener('resize', () => this.onWindowResize(), false);
    }
    
    // 初始化灯光
    initLights() {
        console.log("初始化灯光...");
        // 环境光
        this.ambientLight = new THREE.AmbientLight(0x606060, 1);
        this.scene.add(this.ambientLight);
        
        // 主方向光（会被环境系统控制）
        this.directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        this.directionalLight.position.set(50, 100, 30);
        this.directionalLight.castShadow = true;
        
        // 配置阴影
        this.directionalLight.shadow.mapSize.width = 2048;
        this.directionalLight.shadow.mapSize.height = 2048;
        this.directionalLight.shadow.camera.left = -100;
        this.directionalLight.shadow.camera.right = 100;
        this.directionalLight.shadow.camera.top = 100;
        this.directionalLight.shadow.camera.bottom = -100;
        this.directionalLight.shadow.camera.near = 0.5;
        this.directionalLight.shadow.camera.far = 500;
        
        this.scene.add(this.directionalLight);
    }
    
    // 初始化地面
    initGround() {
        console.log("初始化地面...");
        // 创建地面
        const groundGeometry = new THREE.PlaneGeometry(500, 500);
        const groundMaterial = new THREE.MeshStandardMaterial({
            color: 0x4CA154,  // 草地绿色
            roughness: 0.8,
            metalness: 0.1
        });
        this.ground = new THREE.Mesh(groundGeometry, groundMaterial);
        this.ground.rotation.x = -Math.PI / 2; // 水平放置
        this.ground.position.y = -0.1; // 稍微下移，避免与道路重叠
        this.ground.receiveShadow = true;
        this.scene.add(this.ground);
    }
    
    // 初始化控制器
    initControls() {
        console.log("初始化相机控制器...");
        try {
            this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
            this.controls.enableDamping = true;
            this.controls.dampingFactor = 0.05;
            this.controls.screenSpacePanning = false;
            this.controls.minDistance = 10;
            this.controls.maxDistance = 200;
            this.controls.maxPolarAngle = Math.PI / 2 - 0.1; // 限制不能从下方看
            console.log("相机控制器初始化成功");
        } catch (e) {
            console.error("初始化相机控制器失败:", e);
        }
    }
    
    // 初始化所有城市系统
    initCitySystems() {
        console.log("初始化城市系统...");
        
        // 初始化道路系统
        this.roadSystem = new RoadSystem(this.scene);
        this.roadSystem.generateGrid(100, 40); // 生成10x10的道路网格，间距40米
        
        // 初始化建筑系统
        this.buildingSystem = new BuildingSystem(this.scene);
        this.buildingSystem.generateCity(30); // 生成30栋建筑物
        
        // 初始化街道元素（路灯、树木、长椅等）
        this.streetElements = new StreetElements(this.scene, this.roadSystem);
        this.streetElements.generateElements();
        
        // 初始化车辆系统
        this.vehicleSystem = new VehicleSystem(this.scene, this.roadSystem);
        this.vehicleSystem.generateVehicles(15); // 生成15辆车
        
        // 初始化行人系统
        this.pedestrianSystem = new PedestrianSystem(this.scene, this.roadSystem, this.streetElements);
        this.pedestrianSystem.generatePedestrians(20); // 生成20个行人
        
        console.log("城市系统初始化完成");
    }
    
    // 添加调试辅助工具
    addDebugHelpers() {
        // 坐标轴辅助
        const axesHelper = new THREE.AxesHelper(5);
        this.scene.add(axesHelper);
        
        // 更新调试面板
        this.updateDebugPanel();
    }
    
    // 更新调试面板信息
    updateDebugPanel() {
        const panel = document.getElementById('debug-panel');
        if (panel) {
            this.frameCount++;
            const currentTime = performance.now();
            
            // 每秒更新一次FPS
            if (currentTime - this.lastTime >= 1000) {
                this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
                this.frameCount = 0;
                this.lastTime = currentTime;
                
                // 更新调试面板
                const fpsElement = document.getElementById('fps');
                const buildingCountElement = document.getElementById('building-count');
                const vehicleCountElement = document.getElementById('vehicle-count');
                const pedestrianCountElement = document.getElementById('pedestrian-count');
                const timeElement = document.getElementById('current-time');
                
                if (fpsElement) fpsElement.textContent = this.fps;
                if (buildingCountElement) buildingCountElement.textContent = this.buildingSystem.buildings.length;
                if (vehicleCountElement) vehicleCountElement.textContent = this.vehicleSystem.vehicles.length;
                if (pedestrianCountElement) pedestrianCountElement.textContent = this.pedestrianSystem.pedestrians.length;
                if (timeElement && this.environment) timeElement.textContent = this.environment.getCurrentTimeString();
            }
        }
    }
    
    // 处理窗口大小变化
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    // 动画循环
    animate() {
        requestAnimationFrame(() => this.animate());
        
        // 获取时间增量
        const delta = this.clock.getDelta();
        
        // 更新控制器
        if (this.controls) {
            this.controls.update();
        }
        
        // 更新环境（昼夜循环）
        if (this.environment) {
            this.environment.update(delta);
        }
        
        // 更新建筑系统
        if (this.buildingSystem) {
            this.buildingSystem.update(delta, this.environment ? this.environment.getCurrentTime() : 12);
        }
        
        // 更新车辆系统
        if (this.vehicleSystem) {
            this.vehicleSystem.update(delta);
        }
        
        // 更新行人系统
        if (this.pedestrianSystem) {
            this.pedestrianSystem.update(delta);
        }
        
        // 更新调试面板
        this.updateDebugPanel();
        
        // 渲染场景
        this.renderer.render(this.scene, this.camera);
    }
} 