/**
 * 环境系统 - 负责管理天空、光照和时间系统（昼夜循环）
 */
class Environment {
    constructor(scene) {
        this.scene = scene;
        
        // 时间相关参数
        this.startHour = 12; // 初始时间（中午）
        this.currentTime = this.startHour;
        this.timeSpeed = 0.1; // 时间流逝速度（小时/秒）
        this.isPaused = false;
        
        // 初始化天空和光照
        this.createSkybox();
        this.setupLighting();
        this.createTimeUI();
        
        console.log("环境系统初始化完成");
    }
    
    // 创建天空盒
    createSkybox() {
        // 创建天空盒材质
        this.daySkyColor = new THREE.Color(0x87CEEB); // 天蓝色
        this.nightSkyColor = new THREE.Color(0x0A1020); // 深蓝色
        this.sunsetSkyColor = new THREE.Color(0xFFA07A); // 橙红色
        
        // 设置初始天空颜色
        this.scene.background = this.daySkyColor.clone();
        this.scene.fog.color = this.daySkyColor.clone();
    }
    
    // 设置光照系统
    setupLighting() {
        // 查找场景中已有的光源
        this.ambientLight = null;
        this.sunLight = null;
        this.moonLight = null;
        
        this.scene.children.forEach(child => {
            if (child instanceof THREE.AmbientLight) {
                this.ambientLight = child;
            } else if (child instanceof THREE.DirectionalLight) {
                this.sunLight = child;
            }
        });
        
        // 如果没有找到环境光，则创建
        if (!this.ambientLight) {
            this.ambientLight = new THREE.AmbientLight(0x606060, 1);
            this.scene.add(this.ambientLight);
        }
        
        // 如果没有找到方向光，则创建太阳光
        if (!this.sunLight) {
            this.sunLight = new THREE.DirectionalLight(0xffffff, 1);
            this.sunLight.position.set(50, 100, 30);
            this.sunLight.castShadow = true;
            this.scene.add(this.sunLight);
        }
        
        // 创建月光（夜晚使用）
        this.moonLight = new THREE.DirectionalLight(0x8080FF, 0.5);
        this.moonLight.position.set(-50, 100, -30);
        this.moonLight.castShadow = true;
        this.moonLight.visible = false;
        this.scene.add(this.moonLight);
        
        // 设置光照强度的初始值
        this.dayAmbientIntensity = 1.0;
        this.nightAmbientIntensity = 0.2;
        this.daySunIntensity = 1.0;
        this.nightSunIntensity = 0.0;
        this.moonIntensity = 0.5;
    }
    
    // 创建时间控制UI
    createTimeUI() {
        // 查找或创建时间控制UI容器
        let timeControl = document.getElementById('time-control');
        
        if (!timeControl) {
            timeControl = document.createElement('div');
            timeControl.id = 'time-control';
            document.body.appendChild(timeControl);
            
            // 创建当前时间显示
            const timeDisplay = document.createElement('div');
            timeDisplay.id = 'time-display';
            timeDisplay.textContent = this.getTimeString(this.currentTime);
            timeControl.appendChild(timeDisplay);
            
            // 创建时间滑块
            const timeSlider = document.createElement('input');
            timeSlider.type = 'range';
            timeSlider.id = 'time-slider';
            timeSlider.min = 0;
            timeSlider.max = 24;
            timeSlider.step = 0.1;
            timeSlider.value = this.currentTime;
            timeControl.appendChild(timeSlider);
            
            // 创建暂停/播放按钮
            const pauseButton = document.createElement('button');
            pauseButton.id = 'pause-button';
            pauseButton.textContent = '暂停';
            timeControl.appendChild(pauseButton);
            
            // 创建速度控制
            const speedLabel = document.createElement('span');
            speedLabel.textContent = '速度: ';
            timeControl.appendChild(speedLabel);
            
            const speedSlider = document.createElement('input');
            speedSlider.type = 'range';
            speedSlider.id = 'speed-slider';
            speedSlider.min = 0.01;
            speedSlider.max = 0.5;
            speedSlider.step = 0.01;
            speedSlider.value = this.timeSpeed;
            speedSlider.style.width = '100px';
            timeControl.appendChild(speedSlider);
            
            // 事件监听
            timeSlider.addEventListener('input', () => {
                this.currentTime = parseFloat(timeSlider.value);
                timeDisplay.textContent = this.getTimeString(this.currentTime);
                this.updateEnvironment(0); // 立即更新环境
            });
            
            pauseButton.addEventListener('click', () => {
                this.isPaused = !this.isPaused;
                pauseButton.textContent = this.isPaused ? '播放' : '暂停';
            });
            
            speedSlider.addEventListener('input', () => {
                this.timeSpeed = parseFloat(speedSlider.value);
            });
        }
    }
    
    // 更新环境（根据时间）
    update(deltaTime) {
        // 如果未暂停，更新时间
        if (!this.isPaused) {
            this.currentTime += deltaTime * this.timeSpeed;
            if (this.currentTime >= 24) {
                this.currentTime -= 24;
            }
            
            // 更新UI
            const timeDisplay = document.getElementById('time-display');
            const timeSlider = document.getElementById('time-slider');
            
            if (timeDisplay) {
                timeDisplay.textContent = this.getTimeString(this.currentTime);
            }
            
            if (timeSlider) {
                timeSlider.value = this.currentTime;
            }
        }
        
        // 更新环境效果
        this.updateEnvironment(deltaTime);
    }
    
    // 根据当前时间更新环境效果
    updateEnvironment(deltaTime) {
        // 根据时间更新天空颜色
        let skyColor = new THREE.Color();
        let fogColor = new THREE.Color();
        let ambientIntensity = 0;
        let sunIntensity = 0;
        
        // 确定白天还是夜晚，或者是过渡期（日出/日落）
        // 时间对应关系：
        // 0:00 - 5:00: 夜晚
        // 5:00 - 7:00: 日出（过渡）
        // 7:00 - 19:00: 白天
        // 19:00 - 21:00: 日落（过渡）
        // 21:00 - 24:00: 夜晚
        
        if (this.currentTime >= 7 && this.currentTime < 19) {
            // 白天
            skyColor.copy(this.daySkyColor);
            fogColor.copy(this.daySkyColor);
            ambientIntensity = this.dayAmbientIntensity;
            sunIntensity = this.daySunIntensity;
            this.sunLight.visible = true;
            this.moonLight.visible = false;
            
        } else if (this.currentTime >= 21 || this.currentTime < 5) {
            // 夜晚
            skyColor.copy(this.nightSkyColor);
            fogColor.copy(this.nightSkyColor);
            ambientIntensity = this.nightAmbientIntensity;
            sunIntensity = this.nightSunIntensity;
            this.sunLight.visible = false;
            this.moonLight.visible = true;
            
        } else if (this.currentTime >= 5 && this.currentTime < 7) {
            // 日出
            const t = (this.currentTime - 5) / 2; // 0到1的过渡系数
            skyColor.lerpColors(this.nightSkyColor, this.sunsetSkyColor, t);
            fogColor.lerpColors(this.nightSkyColor, this.sunsetSkyColor, t);
            ambientIntensity = THREE.MathUtils.lerp(this.nightAmbientIntensity, this.dayAmbientIntensity, t);
            sunIntensity = THREE.MathUtils.lerp(this.nightSunIntensity, this.daySunIntensity, t);
            this.sunLight.visible = true;
            this.moonLight.visible = t < 0.5;
            
        } else {
            // 日落
            const t = (this.currentTime - 19) / 2; // 0到1的过渡系数
            skyColor.lerpColors(this.daySkyColor, this.sunsetSkyColor, t);
            fogColor.lerpColors(this.daySkyColor, this.sunsetSkyColor, t);
            if (t > 0.5) {
                const t2 = (t - 0.5) * 2; // 0到1的第二阶段过渡
                skyColor.lerpColors(this.sunsetSkyColor, this.nightSkyColor, t2);
                fogColor.lerpColors(this.sunsetSkyColor, this.nightSkyColor, t2);
            }
            ambientIntensity = THREE.MathUtils.lerp(this.dayAmbientIntensity, this.nightAmbientIntensity, t);
            sunIntensity = THREE.MathUtils.lerp(this.daySunIntensity, this.nightSunIntensity, t);
            this.sunLight.visible = t < 0.8;
            this.moonLight.visible = t > 0.5;
        }
        
        // 应用计算出的环境参数
        this.scene.background.copy(skyColor);
        this.scene.fog.color.copy(fogColor);
        this.ambientLight.intensity = ambientIntensity;
        this.sunLight.intensity = sunIntensity;
        
        // 更新太阳和月亮位置
        this.updateCelestialBodies();
    }
    
    // 更新太阳和月亮位置
    updateCelestialBodies() {
        // 太阳位置：根据时间在天空中移动
        const sunAngle = (this.currentTime / 24) * Math.PI * 2 - Math.PI / 2;
        const sunRadius = 100;
        const sunHeight = Math.sin(sunAngle) * sunRadius;
        const sunForward = Math.cos(sunAngle) * sunRadius;
        
        this.sunLight.position.set(sunForward, sunHeight + 10, 0);
        
        // 月亮位置：与太阳相反
        this.moonLight.position.set(-sunForward, -sunHeight + 40, 0);
    }
    
    // 获取格式化的时间字符串
    getTimeString(hours) {
        const h = Math.floor(hours);
        const m = Math.floor((hours - h) * 60);
        return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
    }
    
    // 获取当前时间（小时，浮点数）
    getCurrentTime() {
        return this.currentTime;
    }
    
    // 获取当前时间（格式化字符串）
    getCurrentTimeString() {
        return this.getTimeString(this.currentTime);
    }
} 