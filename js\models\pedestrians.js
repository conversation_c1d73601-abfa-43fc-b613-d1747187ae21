/**
 * 行人系统 - 负责创建和管理城市中的行人
 */
class PedestrianSystem {
    constructor(scene, roadSystem, streetElements) {
        this.scene = scene;
        this.roadSystem = roadSystem;
        this.streetElements = streetElements;
        this.pedestrians = [];
        this.materials = this.createMaterials();
        this.spawnTimer = 0;
        this.spawnInterval = 3; // 行人生成间隔（秒）
        this.maxPedestrians = 30; // 最大行人数量
    }
    
    createMaterials() {
        return {
            // 皮肤材质（不同肤色）
            skin: [
                new THREE.MeshPhongMaterial({ color: 0xffe0bd, specular: 0x101010, shininess: 30 }),
                new THREE.MeshPhongMaterial({ color: 0xd1b995, specular: 0x101010, shininess: 30 }),
                new THREE.MeshPhongMaterial({ color: 0xc69076, specular: 0x101010, shininess: 30 }),
                new THREE.MeshPhongMaterial({ color: 0xa57156, specular: 0x101010, shininess: 30 }),
                new THREE.MeshPhongMaterial({ color: 0x8d5524, specular: 0x101010, shininess: 30 })
            ],
            // 衣服材质（不同颜色）
            clothes: [
                new THREE.MeshPhongMaterial({ color: 0x3060a0, specular: 0x303030, shininess: 10 }), // 蓝色
                new THREE.MeshPhongMaterial({ color: 0x802020, specular: 0x303030, shininess: 10 }), // 红色
                new THREE.MeshPhongMaterial({ color: 0x106010, specular: 0x303030, shininess: 10 }), // 绿色
                new THREE.MeshPhongMaterial({ color: 0x303030, specular: 0x404040, shininess: 10 }), // 黑色
                new THREE.MeshPhongMaterial({ color: 0xb06020, specular: 0x404040, shininess: 10 }), // 橙色
                new THREE.MeshPhongMaterial({ color: 0x707070, specular: 0x404040, shininess: 10 }), // 灰色
                new THREE.MeshPhongMaterial({ color: 0x502080, specular: 0x404040, shininess: 10 })  // 紫色
            ],
            // 裤子材质
            pants: [
                new THREE.MeshPhongMaterial({ color: 0x202060, specular: 0x202020, shininess: 5 }), // 深蓝色
                new THREE.MeshPhongMaterial({ color: 0x202020, specular: 0x202020, shininess: 5 }), // 黑色
                new THREE.MeshPhongMaterial({ color: 0x303030, specular: 0x202020, shininess: 5 }), // 深灰色
                new THREE.MeshPhongMaterial({ color: 0x704030, specular: 0x202020, shininess: 5 })  // 棕色
            ],
            // 头发材质
            hair: [
                new THREE.MeshPhongMaterial({ color: 0x000000, specular: 0x202020, shininess: 40 }), // 黑色
                new THREE.MeshPhongMaterial({ color: 0x301800, specular: 0x202020, shininess: 40 }), // 深棕色
                new THREE.MeshPhongMaterial({ color: 0x603000, specular: 0x202020, shininess: 40 }), // 棕色
                new THREE.MeshPhongMaterial({ color: 0xc0c0c0, specular: 0x202020, shininess: 40 })  // 灰色/白色
            ],
            // 鞋子材质
            shoes: new THREE.MeshPhongMaterial({ color: 0x101010, specular: 0x404040, shininess: 60 })
        };
    }
    
    // 生成初始行人
    generatePedestrians(count = 20) {
        // 清除现有行人
        this.clearPedestrians();
        
        // 生成新行人
        for (let i = 0; i < count; i++) {
            this.spawnPedestrian();
        }
    }
    
    // 清除现有行人
    clearPedestrians() {
        for (const pedestrian of this.pedestrians) {
            this.scene.remove(pedestrian.mesh);
        }
        this.pedestrians = [];
    }
    
    // 生成单个行人
    spawnPedestrian() {
        // 确保道路系统已初始化
        if (!this.roadSystem || !this.roadSystem.roads || this.roadSystem.roads.length === 0) {
            console.error("无法生成行人：道路系统未初始化");
            return;
        }
        
        // 获取行人的随机位置（人行道或道路附近）
        const position = this.getRandomPedestrianPosition();
        if (!position) return;
        
        // 随机选择行人类型和高度
        const height = 1.6 + Math.random() * 0.3; // 1.6-1.9米
        const isFemale = Math.random() < 0.5;
        
        // 创建行人模型
        const pedestrian = this.createPedestrian(height, isFemale);
        if (!pedestrian) return;
        
        // 设置行人的初始位置
        pedestrian.mesh.position.copy(position);
        pedestrian.mesh.position.y = height / 2; // 调整高度，使行人脚部在地面上
        
        // 设置行人的随机朝向
        pedestrian.mesh.rotation.y = Math.random() * Math.PI * 2;
        
        // 设置行人的行走速度和方向
        pedestrian.speed = 0.5 + Math.random() * 1.5; // 0.5-2.0 米/秒
        pedestrian.direction = new THREE.Vector3(
            Math.cos(pedestrian.mesh.rotation.y),
            0,
            Math.sin(pedestrian.mesh.rotation.y)
        );
        
        // 设置行人的状态
        pedestrian.state = 'walking'; // walking, waiting, sitting
        pedestrian.stateTime = 5 + Math.random() * 15; // 当前状态持续时间
        pedestrian.animationTime = 0; // 动画计时器
        
        // 添加到场景和行人列表
        this.scene.add(pedestrian.mesh);
        this.pedestrians.push(pedestrian);
    }
    
    // 获取行人的随机位置
    getRandomPedestrianPosition() {
        // 尝试多次找到合适的位置
        for (let attempt = 0; attempt < 10; attempt++) {
            // 随机选择一条道路
            const road = this.roadSystem.roads[Math.floor(Math.random() * this.roadSystem.roads.length)];
            const roadBbox = new THREE.Box3().setFromObject(road);
            
            // 判断道路方向
            const isHorizontal = (roadBbox.max.x - roadBbox.min.x) > (roadBbox.max.z - roadBbox.min.z);
            
            // 计算人行道位置（道路两侧）
            let position = new THREE.Vector3();
            const sidewalkDistance = 8; // 人行道距离道路中心的距离
            
            if (isHorizontal) {
                position.x = roadBbox.min.x + Math.random() * (roadBbox.max.x - roadBbox.min.x);
                position.z = road.position.z + (Math.random() < 0.5 ? sidewalkDistance : -sidewalkDistance);
            } else {
                position.x = road.position.x + (Math.random() < 0.5 ? sidewalkDistance : -sidewalkDistance);
                position.z = roadBbox.min.z + Math.random() * (roadBbox.max.z - roadBbox.min.z);
            }
            
            // 检查该位置是否与街道元素冲突
            if (this.streetElements && this.isPositionClear(position)) {
                return position;
            }
        }
        
        // 如果找不到合适位置，返回随机位置
        return new THREE.Vector3(
            (Math.random() - 0.5) * 200,
            0,
            (Math.random() - 0.5) * 200
        );
    }
    
    // 检查位置是否没有冲突
    isPositionClear(position) {
        // 如果没有街道元素，则位置可用
        if (!this.streetElements || !this.streetElements.elements) {
            return true;
        }
        
        // 检查与街道元素的距离
        for (const type in this.streetElements.elements) {
            for (const element of this.streetElements.elements[type]) {
                const distance = position.distanceTo(element.position);
                if (distance < 1.5) { // 如果与元素距离太近
                    return false;
                }
            }
        }
        
        return true;
    }
    
    // 创建行人模型
    createPedestrian(height = 1.8, isFemale = false) {
        const group = new THREE.Group();
        
        // 随机选择材质
        const skinMaterial = this.materials.skin[Math.floor(Math.random() * this.materials.skin.length)];
        const clothesMaterial = this.materials.clothes[Math.floor(Math.random() * this.materials.clothes.length)];
        const pantsMaterial = this.materials.pants[Math.floor(Math.random() * this.materials.pants.length)];
        const hairMaterial = this.materials.hair[Math.floor(Math.random() * this.materials.hair.length)];
        
        // 比例系数
        const scale = height / 1.8;
        
        // 头部
        const headGeometry = new THREE.SphereGeometry(0.15 * scale, 12, 12);
        const head = new THREE.Mesh(headGeometry, skinMaterial);
        head.position.y = height - 0.15 * scale;
        head.castShadow = true;
        group.add(head);
        
        // 头发
        const hairGeometry = isFemale 
            ? new THREE.SphereGeometry(0.17 * scale, 12, 12, 0, Math.PI * 2, 0, Math.PI * 0.6) 
            : new THREE.SphereGeometry(0.16 * scale, 12, 12, 0, Math.PI * 2, 0, Math.PI * 0.3);
        const hair = new THREE.Mesh(hairGeometry, hairMaterial);
        hair.position.y = height - 0.12 * scale;
        hair.castShadow = true;
        group.add(hair);
        
        // 身体
        const bodyHeight = height * 0.3;
        const bodyGeometry = new THREE.CylinderGeometry(
            0.13 * scale, // 顶部半径
            0.15 * scale, // 底部半径
            bodyHeight,   // 高度
            8             // 分段
        );
        const body = new THREE.Mesh(bodyGeometry, clothesMaterial);
        body.position.y = height - 0.3 * scale - bodyHeight / 2;
        body.castShadow = true;
        group.add(body);
        
        // 腿部
        const legHeight = height * 0.45;
        const legGeometry = new THREE.CylinderGeometry(0.05 * scale, 0.05 * scale, legHeight, 8);
        
        // 左腿
        const leftLeg = new THREE.Mesh(legGeometry, pantsMaterial);
        leftLeg.position.set(0.06 * scale, legHeight / 2, 0);
        leftLeg.castShadow = true;
        group.add(leftLeg);
        
        // 右腿
        const rightLeg = new THREE.Mesh(legGeometry, pantsMaterial);
        rightLeg.position.set(-0.06 * scale, legHeight / 2, 0);
        rightLeg.castShadow = true;
        group.add(rightLeg);
        
        // 鞋子
        const shoeGeometry = new THREE.BoxGeometry(0.08 * scale, 0.05 * scale, 0.15 * scale);
        
        // 左鞋
        const leftShoe = new THREE.Mesh(shoeGeometry, this.materials.shoes);
        leftShoe.position.set(0.06 * scale, 0.025 * scale, 0.04 * scale);
        leftShoe.castShadow = true;
        group.add(leftShoe);
        
        // 右鞋
        const rightShoe = new THREE.Mesh(shoeGeometry, this.materials.shoes);
        rightShoe.position.set(-0.06 * scale, 0.025 * scale, 0.04 * scale);
        rightShoe.castShadow = true;
        group.add(rightShoe);
        
        // 手臂
        const armHeight = height * 0.35;
        const armGeometry = new THREE.CylinderGeometry(0.04 * scale, 0.04 * scale, armHeight, 8);
        
        // 左臂
        const leftArm = new THREE.Mesh(armGeometry, clothesMaterial);
        leftArm.position.set(0.2 * scale, height - 0.3 * scale - armHeight / 2, 0);
        leftArm.castShadow = true;
        group.add(leftArm);
        
        // 右臂
        const rightArm = new THREE.Mesh(armGeometry, clothesMaterial);
        rightArm.position.set(-0.2 * scale, height - 0.3 * scale - armHeight / 2, 0);
        rightArm.castShadow = true;
        group.add(rightArm);
        
        // 保存四肢引用以便动画
        const limbs = {
            leftLeg,
            rightLeg,
            leftArm,
            rightArm
        };
        
        return {
            mesh: group,
            limbs: limbs,
            height: height,
            isFemale: isFemale,
            speed: 0,
            direction: null,
            state: 'idle',
            stateTime: 0,
            animationTime: 0
        };
    }
    
    // 更新行人状态
    update(deltaTime) {
        // 定期生成新行人
        this.spawnTimer += deltaTime;
        if (this.spawnTimer >= this.spawnInterval) {
            this.spawnTimer = 0;
            if (this.pedestrians.length < this.maxPedestrians) {
                this.spawnPedestrian();
            }
        }
        
        // 更新现有行人
        for (let i = this.pedestrians.length - 1; i >= 0; i--) {
            const pedestrian = this.pedestrians[i];
            
            // 更新状态计时器
            pedestrian.stateTime -= deltaTime;
            pedestrian.animationTime += deltaTime * 3; // 动画速度因子
            
            // 如果当前状态时间结束，随机切换状态
            if (pedestrian.stateTime <= 0) {
                this.changeState(pedestrian);
            }
            
            // 根据状态执行行为
            switch (pedestrian.state) {
                case 'walking':
                    this.updateWalking(pedestrian, deltaTime);
                    break;
                case 'waiting':
                    this.updateWaiting(pedestrian, deltaTime);
                    break;
                case 'sitting':
                    this.updateSitting(pedestrian, deltaTime);
                    break;
                default:
                    break;
            }
        }
    }
    
    // 改变行人状态
    changeState(pedestrian) {
        // 根据当前状态和随机因素确定下一个状态
        const rand = Math.random();
        
        switch (pedestrian.state) {
            case 'walking':
                if (rand < 0.2) {
                    pedestrian.state = 'waiting';
                    pedestrian.stateTime = 2 + Math.random() * 5; // 等待2-7秒
                } else if (rand < 0.25 && this.findNearbyBench(pedestrian.mesh.position)) {
                    pedestrian.state = 'sitting';
                    pedestrian.stateTime = 10 + Math.random() * 20; // 坐下10-30秒
                } else {
                    // 继续行走，但可能改变方向
                    const angle = Math.random() * Math.PI / 2 - Math.PI / 4;
                    const currentAngle = Math.atan2(pedestrian.direction.z, pedestrian.direction.x);
                    const newAngle = currentAngle + angle;
                    
                    pedestrian.direction.x = Math.cos(newAngle);
                    pedestrian.direction.z = Math.sin(newAngle);
                    pedestrian.mesh.rotation.y = newAngle;
                    
                    pedestrian.stateTime = 5 + Math.random() * 10; // 朝新方向行走5-15秒
                }
                break;
                
            case 'waiting':
                if (rand < 0.8) {
                    pedestrian.state = 'walking';
                    pedestrian.stateTime = 5 + Math.random() * 15; // 行走5-20秒
                } else {
                    // 继续等待
                    pedestrian.stateTime = 2 + Math.random() * 3; // 再等待2-5秒
                }
                break;
                
            case 'sitting':
                pedestrian.state = 'walking';
                pedestrian.stateTime = 5 + Math.random() * 10; // 行走5-15秒
                break;
                
            default:
                pedestrian.state = 'walking';
                pedestrian.stateTime = 5 + Math.random() * 10;
                break;
        }
        
        // 重置动画时间
        pedestrian.animationTime = 0;
    }
    
    // 更新行走状态
    updateWalking(pedestrian, deltaTime) {
        // 移动行人
        const moveDistance = pedestrian.speed * deltaTime;
        
        // 应用行走方向移动
        pedestrian.mesh.position.x += pedestrian.direction.x * moveDistance;
        pedestrian.mesh.position.z += pedestrian.direction.z * moveDistance;
        
        // 行走动画 - 摆动双腿和手臂
        const swingAngle = Math.PI / 6; // 30度摆动角度
        const animTime = pedestrian.animationTime;
        
        // 同步摆动双腿和手臂，使用正弦函数实现周期性摆动
        const legAngle = Math.sin(animTime) * swingAngle;
        const armAngle = Math.sin(animTime) * swingAngle;
        
        // 应用腿部摆动
        pedestrian.limbs.leftLeg.rotation.x = legAngle;
        pedestrian.limbs.rightLeg.rotation.x = -legAngle;
        
        // 应用手臂摆动（与腿部相反）
        pedestrian.limbs.leftArm.rotation.x = -armAngle;
        pedestrian.limbs.rightArm.rotation.x = armAngle;
        
        // 检查是否到达边界，如果是，则改变方向
        if (this.isNearBoundary(pedestrian.mesh.position)) {
            // 反转方向
            pedestrian.direction.x *= -1;
            pedestrian.direction.z *= -1;
            pedestrian.mesh.rotation.y += Math.PI; // 旋转180度
        }
    }
    
    // 更新等待状态
    updateWaiting(pedestrian, deltaTime) {
        // 等待动画 - 轻微摆动或保持静止
        const swingAngle = Math.PI / 30; // 很小的摆动角度
        const animTime = pedestrian.animationTime;
        
        // 轻微摆动身体（通过摆动手臂实现）
        const armAngle = Math.sin(animTime) * swingAngle;
        
        // 应用轻微摆动
        pedestrian.limbs.leftArm.rotation.x = armAngle;
        pedestrian.limbs.rightArm.rotation.x = -armAngle;
        
        // 重置腿部位置
        pedestrian.limbs.leftLeg.rotation.x = 0;
        pedestrian.limbs.rightLeg.rotation.x = 0;
    }
    
    // 更新坐下状态
    updateSitting(pedestrian, deltaTime) {
        // 坐下时腿部弯曲，手臂放在膝盖上
        pedestrian.limbs.leftLeg.rotation.x = Math.PI / 2; // 90度
        pedestrian.limbs.rightLeg.rotation.x = Math.PI / 2;
        
        pedestrian.limbs.leftArm.rotation.x = Math.PI / 4; // 45度
        pedestrian.limbs.rightArm.rotation.x = Math.PI / 4;
    }
    
    // 检查是否接近边界
    isNearBoundary(position) {
        const boundary = 200; // 边界距离
        return (
            position.x < -boundary || 
            position.x > boundary || 
            position.z < -boundary || 
            position.z > boundary
        );
    }
    
    // 查找附近的长凳
    findNearbyBench(position) {
        if (!this.streetElements || !this.streetElements.elements || !this.streetElements.elements.benches) {
            return false;
        }
        
        // 检查是否有长凳在附近
        for (const bench of this.streetElements.elements.benches) {
            const distance = position.distanceTo(bench.position);
            if (distance < 3) { // 如果长凳在3米范围内
                return bench;
            }
        }
        
        return false;
    }
} 