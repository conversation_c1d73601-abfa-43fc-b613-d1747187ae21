/**
 * 相机控制系统
 */

class CameraController {
    constructor(camera, domElement) {
        // 存储相机和DOM元素引用
        this.camera = camera;
        this.domElement = domElement;
        
        // 创建OrbitControls
        this.orbitControls = new THREE.OrbitControls(camera, domElement);
        this.orbitControls.enableDamping = true; // 启用阻尼效果，使相机移动更平滑
        this.orbitControls.dampingFactor = 0.05;
        this.orbitControls.screenSpacePanning = false;
        this.orbitControls.minDistance = 5;    // 最小缩放距离
        this.orbitControls.maxDistance = 500;  // 最大缩放距离
        this.orbitControls.maxPolarAngle = Math.PI / 2; // 限制垂直旋转角度
        
        // 预设视角
        this.presetViews = {
            street: {
                position: new THREE.Vector3(10, 2, 10),
                target: new THREE.Vector3(20, 2, 20),
                duration: 1000 // 过渡动画持续时间（毫秒）
            },
            aerial: {
                position: new THREE.Vector3(0, 150, 0),
                target: new THREE.Vector3(0, 0, 0),
                duration: 1500
            },
            building: {
                position: new THREE.Vector3(30, 60, 30),
                target: new THREE.Vector3(0, 30, 0),
                duration: 1200
            }
        };
        
        // 动画相关属性
        this.isAnimating = false;
        this.animationStartTime = 0;
        this.animationDuration = 0;
        this.startPosition = new THREE.Vector3();
        this.targetPosition = new THREE.Vector3();
        this.startTarget = new THREE.Vector3();
        this.endTarget = new THREE.Vector3();
        
        // 初始化事件监听
        this.initEventListeners();
    }
    
    // 初始化事件监听器
    initEventListeners() {
        // 视角按钮点击事件
        document.getElementById('view-street').addEventListener('click', () => {
            this.transitionToView('street');
        });
        
        document.getElementById('view-aerial').addEventListener('click', () => {
            this.transitionToView('aerial');
        });
        
        document.getElementById('view-building').addEventListener('click', () => {
            this.transitionToView('building');
        });
    }
    
    // 切换到预设视角
    transitionToView(viewName) {
        if (!this.presetViews[viewName]) {
            console.error(`视角 "${viewName}" 不存在`);
            return;
        }
        
        const preset = this.presetViews[viewName];
        
        // 存储当前相机位置和目标位置
        this.startPosition.copy(this.camera.position);
        this.targetPosition.copy(preset.position);
        
        this.startTarget.copy(this.orbitControls.target);
        this.endTarget.copy(preset.target);
        
        // 设置动画参数
        this.isAnimating = true;
        this.animationStartTime = Date.now();
        this.animationDuration = preset.duration;
        
        // 动画期间禁用控制器
        this.orbitControls.enabled = false;
    }
    
    // 使用自定义函数进行平滑过渡
    easeInOutCubic(t) {
        return t < 0.5
            ? 4 * t * t * t
            : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }
    
    // 更新函数，应该在动画循环中调用
    update() {
        // 处理视角过渡动画
        if (this.isAnimating) {
            const elapsed = Date.now() - this.animationStartTime;
            const progress = Math.min(elapsed / this.animationDuration, 1);
            const easedProgress = this.easeInOutCubic(progress);
            
            // 更新相机位置
            this.camera.position.lerpVectors(
                this.startPosition,
                this.targetPosition,
                easedProgress
            );
            
            // 更新轨道控制器的目标点
            this.orbitControls.target.lerpVectors(
                this.startTarget,
                this.endTarget,
                easedProgress
            );
            
            // 确保相机的矩阵被更新
            this.camera.updateProjectionMatrix();
            
            // 动画完成
            if (progress === 1) {
                this.isAnimating = false;
                this.orbitControls.enabled = true;
            }
        }
        
        // 更新轨道控制器
        this.orbitControls.update();
    }
    
    // 添加碰撞检测（与建筑物碰撞）
    checkCollisions(buildings) {
        // 简单碰撞检测，防止相机进入建筑内部
        // 此处仅为示例，实际应用可能需要更复杂的碰撞检测系统
        const cameraPosition = this.camera.position.clone();
        cameraPosition.y -= 2; // 假设相机位置比眼睛高度高2个单位
        
        for (const building of buildings) {
            if (building.geometry && building.position) {
                // 获取建筑包围盒
                const bbox = new THREE.Box3().setFromObject(building);
                
                // 检查相机是否在包围盒内
                if (bbox.containsPoint(cameraPosition)) {
                    // 如果在内部，将相机推出建筑
                    const buildingCenter = new THREE.Vector3();
                    bbox.getCenter(buildingCenter);
                    
                    const direction = cameraPosition.clone().sub(buildingCenter).normalize();
                    const distance = bbox.min.distanceTo(bbox.max) / 2;
                    
                    this.camera.position.copy(buildingCenter.clone().add(direction.multiplyScalar(distance + 5)));
                    break;
                }
            }
        }
    }
    
    // 销毁控制器，释放内存
    dispose() {
        this.orbitControls.dispose();
        
        // 移除所有事件监听器
        document.getElementById('view-street').removeEventListener('click', this.transitionToView);
        document.getElementById('view-aerial').removeEventListener('click', this.transitionToView);
        document.getElementById('view-building').removeEventListener('click', this.transitionToView);
    }
} 