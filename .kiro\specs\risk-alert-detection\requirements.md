# 风险警情检测系统需求文档

## 介绍

本系统旨在通过AI模型自动识别和筛选警情数据中的风险、敏感、异常警情，提高警务工作效率和风险预警能力。系统将处理包含接警单编号、报警时间、反馈单位、反馈内容等字段的Excel表格，通过深度学习模型分析反馈内容，识别出需要重点关注的警情并生成筛选后的结果表格。

## 需求

### 需求1：Excel文件上传和解析

**用户故事：** 作为警务人员，我希望能够上传包含警情数据的Excel文件，以便系统能够读取和处理其中的数据。

#### 验收标准

1. WHEN 用户上传Excel文件 THEN 系统 SHALL 验证文件格式为.xlsx或.xls
2. WHEN 文件格式正确 THEN 系统 SHALL 解析文件并识别必要字段（接警单编号、报警时间、案发地点、反馈单位、报警内容、反馈内容）
3. WHEN 文件缺少必要字段 THEN 系统 SHALL 提示用户缺少的字段信息
4. WHEN 文件解析成功 THEN 系统 SHALL 显示数据预览和总行数

### 需求2：AI模型风险警情判断

**用户故事：** 作为警务人员，我希望系统能够使用AI模型自动判断每条警情的风险等级，以便快速识别需要重点关注的案件。

#### 验收标准

1. WHEN 系统处理警情数据 THEN 系统 SHALL 使用deepseek-R1或其他指定AI模型分析反馈内容
2. WHEN AI模型分析内容 THEN 系统 SHALL 根据预设的判断标准识别风险、敏感、异常警情
3. WHEN 判断标准基于实际警务标准 THEN 系统 SHALL 识别以下类型的敏感、异常、风险警情：

   **110接报警情类型：**
   - 公共场所打架斗殴、寻衅滋事警情
   - 故意伤害、杀人、抢劫、绑架、涉枪等重大刑事案件
   - 火灾、爆燃等安全生产类警情
   - 自杀、自残等个人极端警情
   - 溺水、高坠、烧炭等非正常死亡警情
   - 侵害妇女、儿童、老年人、精神病患者、残疾人等弱势群体警情
   - 涉及身份敏感人员（学生、教师、医护人员、公职人员、知名人士、记者、军人、法律从业者、外籍人员、涉疆涉藏人员等）的警情
   - 有人员伤亡或多车碰撞或车辆损毁严重的交通事故
   - 涉及警用车辆、警务人员的警情
   - 聚集维权、请愿等群体性事件警情
   - 涉及有损公序良俗、伤害民族感情的警情
   - 因自然灾害造成人员伤亡、重大财产损失的警情
   - 其他易引发公众关注、舆情炒作或媒体报道的警情

   **现场处置异常情况：**
   - 出现当事人、证人或群众聚集围观、拍摄的警情
   - 出现有拍摄人员不听劝阻或直播的警情
   - 出现媒体记者到场采访报道的警情
   - 与现场人员发生肢体或言语冲突的警情
   - 出现未报备、未登记无人机或有固定监控拍摄的警情
   - 现场劝导时与拍摄者发生冲突的警情
   - 出现其他异常情况可能引发舆情的警情

   **其他特殊情况：**
   - 多次纠纷报警且有行为升级、致人伤亡的
   - 多次纠纷报警且有扬言、自杀、涉刀行为的
   - 多次纠纷报警且有暴力前科的
   - 被诈骗后有扬言、自杀、涉刀行为的
   - 非正常死亡警情，对死亡原因、赔付有异议的
   - 涉精神病人有暴力行为，多次报警，家人看管不力，拒绝送医的
   - 涉未成年人非正常死亡、严重欺凌暴力行为、走失24小时未找回的
   - 老人（70岁以上）走失24小时未找回的
   - 近期新出现扬言、自杀行为的
   - 非警务分流，今年以来多次因同一原因报警的
   - 报警涉多人打架斗殴，反馈未发现的
   - 涉多人指向市政府聚集的线索

   **问题警情标准：**
   - 反馈内容不完整、事情表述不完整
   - 因果关系不明确
   - 基本要素（时间、地点、人物、事情、原因等）不齐全
4. WHEN AI模型完成判断 THEN 系统 SHALL 为每条记录标记风险等级（高风险/中风险/低风险/正常）

### 需求3：数据筛选和结果生成

**用户故事：** 作为警务人员，我希望系统能够筛选出风险警情并生成新的表格，以便我能够专注处理重要案件。

#### 验收标准

1. WHEN AI模型完成所有记录的判断 THEN 系统 SHALL 筛选出标记为风险、敏感、异常的警情记录
2. WHEN 筛选完成 THEN 系统 SHALL 保留筛选记录的所有原始字段数据
3. WHEN 生成结果表格 THEN 系统 SHALL 添加风险等级和AI判断理由字段
4. WHEN 结果表格生成 THEN 系统 SHALL 提供Excel格式的下载文件

### 需求4：工作流配置和管理

**用户故事：** 作为系统管理员，我希望能够配置和管理Dify工作流，以便根据实际需求调整判断标准和处理流程。

#### 验收标准

1. WHEN 管理员配置工作流 THEN 系统 SHALL 提供可视化的工作流编辑界面
2. WHEN 配置判断标准 THEN 系统 SHALL 允许自定义风险关键词和判断规则
3. WHEN 配置AI模型 THEN 系统 SHALL 支持切换不同的AI模型（deepseek-R1、GPT-4等）
4. WHEN 工作流运行 THEN 系统 SHALL 记录处理日志和错误信息
5. WHEN 处理大量数据 THEN 系统 SHALL 支持批量处理和进度显示

### 需求5：系统性能和可靠性

**用户故事：** 作为警务人员，我希望系统能够稳定高效地处理大量警情数据，以便在紧急情况下快速获得分析结果。

#### 验收标准

1. WHEN 处理1000条以内的警情记录 THEN 系统 SHALL 在5分钟内完成处理
2. WHEN 系统遇到网络错误 THEN 系统 SHALL 自动重试并记录错误日志
3. WHEN AI模型调用失败 THEN 系统 SHALL 提供备用处理方案或人工标记选项
4. WHEN 数据处理中断 THEN 系统 SHALL 支持断点续传功能
5. WHEN 系统负载过高 THEN 系统 SHALL 提供队列管理和优先级处理