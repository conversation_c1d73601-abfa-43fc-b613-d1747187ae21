/**
 * 道路系统 - 简化版本
 */

class RoadSystem {
    constructor(scene) {
        this.scene = scene;
        this.roads = [];
        this.materials = this.createMaterials();
    }

    createMaterials() {
        return {
            road: new THREE.MeshPhongMaterial({
                color: 0x333333,
                specular: 0x111111,
                shininess: 10
            }),
            line: new THREE.MeshPhongMaterial({
                color: 0xFFFFFF,
                specular: 0xFFFFFF,
                shininess: 30
            })
        };
    }

    createRoad(startX, startZ, endX, endZ, width = 20) {
        // 计算道路长度和角度
        const length = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endZ - startZ, 2));
        const angle = Math.atan2(endZ - startZ, endX - startX);

        // 创建道路
        const roadGeometry = new THREE.PlaneGeometry(length, width);
        const road = new THREE.Mesh(roadGeometry, this.materials.road);

        // 设置道路位置和旋转
        road.position.set(startX + (endX - startX) / 2, 0.1, startZ + (endZ - startZ) / 2);
        road.rotation.x = -Math.PI / 2;
        road.rotation.z = -angle;
        road.receiveShadow = true;

        // 添加道路标线
        const lineWidth = 0.5;
        const lineGeometry = new THREE.PlaneGeometry(length, lineWidth);
        const line = new THREE.Mesh(lineGeometry, this.materials.line);
        line.position.set(0, 0.1, 0);
        road.add(line);

        this.roads.push(road);
        this.scene.add(road);
        return road;
    }

    generateGrid(size = 200, spacing = 50) {
        // 生成网格状道路
        const halfSize = size / 2;
        const count = Math.floor(size / spacing);

        // 创建水平道路
        for (let i = 0; i <= count; i++) {
            const pos = -halfSize + i * spacing;
            this.createRoad(-halfSize, pos, halfSize, pos);
        }

        // 创建垂直道路
        for (let i = 0; i <= count; i++) {
            const pos = -halfSize + i * spacing;
            this.createRoad(pos, -halfSize, pos, halfSize);
        }
    }

    update(time) {
        // 可以在这里添加动画效果
    }
} 