# 风险警情检测系统设计文档

## 概述

风险警情检测系统是一个基于Dify工作流的AI驱动解决方案，用于自动识别和筛选警情数据中的风险、敏感、异常警情。系统采用模块化设计，通过Excel文件处理、AI模型分析、数据筛选和结果生成等核心组件，实现高效的警情风险评估。

## 架构设计

### 系统架构图

```mermaid
graph TB
    A[用户上传Excel文件] --> B[文件验证和解析模块]
    B --> C[数据预处理模块]
    C --> D[AI风险判断模块]
    D --> E[结果筛选模块]
    E --> F[Excel生成模块]
    F --> G[结果下载]
    
    H[配置管理模块] --> D
    I[日志记录模块] --> D
    I --> E
    I --> F
    
    subgraph "Dify工作流核心"
        D
        E
        F
    end
    
    subgraph "AI模型层"
        J[DeepSeek-R1]
        K[GPT-4]
        L[其他LLM模型]
    end
    
    D --> J
    D --> K
    D --> L
```

### 技术栈

- **工作流引擎**: Dify Platform
- **AI模型**: DeepSeek-R1 (主要), GPT-4 (备用)
- **文件处理**: Python pandas + openpyxl
- **数据格式**: Excel (.xlsx/.xls)
- **配置管理**: JSON配置文件
- **日志系统**: 结构化日志记录

## 组件和接口设计

### 1. 文件处理组件

#### Excel解析器 (ExcelParser)
```python
class ExcelParser:
    def validate_file(self, file_path: str) -> bool
    def parse_excel(self, file_path: str) -> DataFrame
    def validate_columns(self, df: DataFrame) -> ValidationResult
    def preview_data(self, df: DataFrame, rows: int = 5) -> dict
```

**接口规范:**
- 输入: Excel文件路径
- 输出: 解析后的DataFrame和验证结果
- 异常处理: 文件格式错误、字段缺失、编码问题

#### 必要字段验证
- 接警单编号 (必需)
- 报警时间 (必需)
- 反馈单位 (必需)
- 反馈内容 (必需，用于AI分析)

### 2. AI风险判断组件

#### 风险分析器 (RiskAnalyzer)
```python
class RiskAnalyzer:
    def __init__(self, model_config: dict)
    def analyze_single_record(self, content: str) -> RiskAssessment
    def batch_analyze(self, contents: List[str]) -> List[RiskAssessment]
    def get_risk_level(self, assessment: RiskAssessment) -> str
```

**风险评估数据结构:**
```python
@dataclass
class RiskAssessment:
    risk_level: str  # "高风险", "中风险", "低风险", "正常"
    risk_categories: List[str]  # 匹配的风险类别
    confidence_score: float  # 置信度 (0-1)
    reasoning: str  # AI判断理由
    keywords_matched: List[str]  # 匹配的关键词
```

#### AI提示词模板
```
你是一个专业的警情风险评估专家。请根据以下标准分析警情内容的风险等级：

【风险判断标准】
[详细的判断标准，包括110接报警情类型、现场处置异常情况、其他特殊情况、问题警情标准]

【分析要求】
1. 仔细阅读反馈内容
2. 识别是否包含风险关键词或情况
3. 判断风险等级：高风险/中风险/低风险/正常
4. 提供具体的判断理由
5. 列出匹配的风险类别

【输出格式】
请以JSON格式输出：
{
  "risk_level": "风险等级",
  "risk_categories": ["匹配的风险类别"],
  "confidence_score": 置信度,
  "reasoning": "判断理由",
  "keywords_matched": ["匹配的关键词"]
}

【待分析内容】
{content}
```

### 3. 数据筛选组件

#### 结果筛选器 (ResultFilter)
```python
class ResultFilter:
    def filter_risk_records(self, df: DataFrame, assessments: List[RiskAssessment]) -> DataFrame
    def add_assessment_columns(self, df: DataFrame, assessments: List[RiskAssessment]) -> DataFrame
    def generate_summary_stats(self, assessments: List[RiskAssessment]) -> dict
```

**筛选逻辑:**
- 保留风险等级为"高风险"、"中风险"、"低风险"的记录
- 排除标记为"正常"的记录
- 添加风险等级、判断理由、匹配关键词等新字段

### 4. Excel生成组件

#### Excel生成器 (ExcelGenerator)
```python
class ExcelGenerator:
    def create_result_excel(self, filtered_df: DataFrame, output_path: str) -> str
    def add_formatting(self, workbook: Workbook) -> None
    def add_summary_sheet(self, workbook: Workbook, stats: dict) -> None
```

**输出Excel结构:**
- Sheet1: 筛选后的风险警情数据
- Sheet2: 统计汇总信息
- 格式化: 风险等级颜色标记、自动列宽调整

## 数据模型

### 输入数据模型
```python
@dataclass
class PoliceAlert:
    alert_id: str  # 接警单编号
    report_time: datetime  # 报警时间
    feedback_unit: str  # 反馈单位
    feedback_content: str  # 反馈内容
    additional_fields: dict  # 其他字段
```

### 输出数据模型
```python
@dataclass
class RiskAlertResult:
    alert_id: str
    report_time: datetime
    feedback_unit: str
    feedback_content: str
    risk_level: str
    risk_categories: List[str]
    confidence_score: float
    reasoning: str
    keywords_matched: List[str]
    processing_time: datetime
    additional_fields: dict
```

## 错误处理

### 错误类型和处理策略

1. **文件处理错误**
   - 文件格式不支持: 提示用户转换格式
   - 文件损坏: 提供修复建议
   - 字段缺失: 明确指出缺失字段

2. **AI模型调用错误**
   - 网络超时: 自动重试3次
   - API限流: 延迟重试
   - 模型不可用: 切换备用模型

3. **数据处理错误**
   - 内存不足: 分批处理
   - 数据格式异常: 跳过异常记录并记录日志

4. **输出生成错误**
   - 磁盘空间不足: 清理临时文件
   - 权限不足: 提示用户检查权限

### 日志记录规范
```python
# 日志级别和内容
INFO: 处理进度、成功操作
WARN: 非致命错误、数据质量问题
ERROR: 系统错误、处理失败
DEBUG: 详细的调试信息
```

## 测试策略

### 单元测试
- Excel解析功能测试
- AI模型调用测试
- 数据筛选逻辑测试
- Excel生成功能测试

### 集成测试
- 端到端工作流测试
- 大数据量处理测试
- 错误恢复测试

### 性能测试
- 1000条记录处理时间测试
- 内存使用量监控
- 并发处理能力测试

### 测试数据
- 正常警情数据样本
- 各类风险警情样本
- 边界情况数据
- 异常格式数据

## 部署和配置

### Dify工作流配置
- 节点类型: 代码执行节点、LLM节点、条件判断节点
- 变量管理: 输入文件路径、模型配置、输出路径
- 错误处理: 异常捕获和重试机制

### 环境要求
- Python 3.8+
- 内存: 最少4GB，推荐8GB
- 存储: 至少1GB可用空间
- 网络: 稳定的互联网连接（用于AI模型调用）

### 配置文件结构
```json
{
  "ai_models": {
    "primary": "deepseek-r1",
    "fallback": "gpt-4",
    "api_keys": {
      "deepseek": "your-api-key",
      "openai": "your-api-key"
    }
  },
  "processing": {
    "batch_size": 50,
    "max_retries": 3,
    "timeout_seconds": 30
  },
  "output": {
    "format": "xlsx",
    "include_summary": true,
    "color_coding": true
  }
}
```