/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: #000;
    color: #fff;
    overflow: hidden;
}

/* 场景容器 */
#scene-container {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: linear-gradient(#1e293b, #0f172a);
}

/* 信息面板 */
#info {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 5px;
    z-index: 100;
    pointer-events: none;
}

#info h1 {
    font-size: 1.5rem;
    margin-bottom: 5px;
    color: #4ade80;
    text-shadow: 0 0 5px rgba(74, 222, 128, 0.8);
}

#controls-info {
    font-size: 0.8rem;
    color: #94a3b8;
}

/* UI容器 */
#ui-container {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(15, 23, 42, 0.8);
    padding: 15px;
    border-radius: 10px;
    z-index: 100;
    display: flex;
    flex-direction: column;
    gap: 10px;
    box-shadow: 0 0 15px rgba(74, 222, 128, 0.5);
}

/* 时间控制 */
#time-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

#time-slider {
    width: 200px;
    cursor: pointer;
}

#time-display {
    min-width: 50px;
    text-align: right;
}

/* 视角控制 */
#view-controls {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

#view-controls button {
    flex: 1;
    padding: 8px;
    background-color: #334155;
    color: #e2e8f0;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s, box-shadow 0.3s;
}

#view-controls button:hover {
    background-color: #475569;
    box-shadow: 0 0 8px rgba(74, 222, 128, 0.8);
}

#view-controls button:active {
    background-color: #4ade80;
    color: #1e293b;
}

/* 响应式样式 */
@media (max-width: 768px) {
    #ui-container {
        width: 90%;
    }
    
    #view-controls {
        flex-direction: column;
    }
    
    #info h1 {
        font-size: 1.2rem;
    }
} 