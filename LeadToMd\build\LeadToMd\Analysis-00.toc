(['F:\\AIGC\\Project\\LeadToMd\\main.py'],
 ['F:\\AIGC\\Project\\LeadToMd'],
 [],
 [('F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main', 'F:\\AIGC\\Project\\LeadToMd\\main.py', 'PYSOURCE')],
 [('subprocess',
   'C:\\Program Files\\Python311\\Lib\\subprocess.py',
   'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python311\\Lib\\selectors.py', 'PYMODULE'),
  ('contextlib',
   'C:\\Program Files\\Python311\\Lib\\contextlib.py',
   'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python311\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python311\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python311\\Lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program Files\\Python311\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program Files\\Python311\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python311\\Lib\\gzip.py', 'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python311\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python311\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python311\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python311\\Lib\\gettext.py', 'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python311\\Lib\\_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program Files\\Python311\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Program Files\\Python311\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'C:\\Program Files\\Python311\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program Files\\Python311\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program Files\\Python311\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program Files\\Python311\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python311\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ipaddress', 'C:\\Program Files\\Python311\\Lib\\ipaddress.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python311\\Lib\\fnmatch.py', 'PYMODULE'),
  ('getpass', 'C:\\Program Files\\Python311\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path',
   'C:\\Program Files\\Python311\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib', 'C:\\Program Files\\Python311\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\Program Files\\Python311\\Lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'C:\\Program Files\\Python311\\Lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Program Files\\Python311\\Lib\\mimetypes.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python311\\Lib\\getopt.py', 'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python311\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python311\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python311\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python311\\Lib\\quopri.py', 'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python311\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python311\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python311\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python311\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python311\\Lib\\calendar.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python311\\Lib\\random.py', 'PYMODULE'),
  ('statistics',
   'C:\\Program Files\\Python311\\Lib\\statistics.py',
   'PYMODULE'),
  ('fractions', 'C:\\Program Files\\Python311\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Program Files\\Python311\\Lib\\numbers.py', 'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program Files\\Python311\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'C:\\Program Files\\Python311\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.cookies',
   'C:\\Program Files\\Python311\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('ssl', 'C:\\Program Files\\Python311\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'C:\\Program Files\\Python311\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program Files\\Python311\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python311\\Lib\\string.py', 'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python311\\Lib\\hashlib.py', 'PYMODULE'),
  ('email',
   'C:\\Program Files\\Python311\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python311\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python311\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python311\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python311\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python311\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python311\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python311\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python311\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python311\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python311\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python311\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python311\\Lib\\email\\header.py',
   'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python311\\Lib\\bisect.py', 'PYMODULE'),
  ('xml.sax',
   'C:\\Program Files\\Python311\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program Files\\Python311\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program Files\\Python311\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program Files\\Python311\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python311\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Program Files\\Python311\\Lib\\http\\client.py',
   'PYMODULE'),
  ('decimal', 'C:\\Program Files\\Python311\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal',
   'C:\\Program Files\\Python311\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program Files\\Python311\\Lib\\contextvars.py',
   'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python311\\Lib\\base64.py', 'PYMODULE'),
  ('hmac', 'C:\\Program Files\\Python311\\Lib\\hmac.py', 'PYMODULE'),
  ('struct', 'C:\\Program Files\\Python311\\Lib\\struct.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python311\\Lib\\socket.py', 'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python311\\Lib\\tempfile.py', 'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python311\\Lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'C:\\Program Files\\Python311\\Lib\\zipfile.py', 'PYMODULE'),
  ('py_compile',
   'C:\\Program Files\\Python311\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python311\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python311\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python311\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python311\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python311\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python311\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Program Files\\Python311\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files\\Python311\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Program Files\\Python311\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Program Files\\Python311\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Program Files\\Python311\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Program Files\\Python311\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program Files\\Python311\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program Files\\Python311\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program Files\\Python311\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program Files\\Python311\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program Files\\Python311\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program Files\\Python311\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python311\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Program Files\\Python311\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Program Files\\Python311\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Program Files\\Python311\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python311\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'C:\\Program Files\\Python311\\Lib\\token.py', 'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python311\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python311\\Lib\\tarfile.py', 'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python311\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python311\\Lib\\bz2.py', 'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python311\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python311\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python311\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'C:\\Program Files\\Python311\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'C:\\Program Files\\Python311\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python311\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python311\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files\\Python311\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python311\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python311\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python311\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python311\\Lib\\ast.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python311\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'C:\\Program Files\\Python311\\Lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python311\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'C:\\Program Files\\Python311\\Lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Program Files\\Python311\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Program Files\\Python311\\Lib\\stringprep.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python311\\Lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python311\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('uvicorn',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\__init__.py',
   'PYMODULE'),
  ('uvicorn.workers',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\workers.py',
   'PYMODULE'),
  ('uvicorn.supervisors.watchgodreload',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\supervisors\\watchgodreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.watchfilesreload',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\supervisors\\watchfilesreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.statreload',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\supervisors\\statreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.multiprocess',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\supervisors\\multiprocess.py',
   'PYMODULE'),
  ('click',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click.utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python311\\Lib\\glob.py', 'PYMODULE'),
  ('typing_extensions',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('click._compat',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('colorama',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('click._winconsole',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.termui',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('tty', 'C:\\Program Files\\Python311\\Lib\\tty.py', 'PYMODULE'),
  ('webbrowser',
   'C:\\Program Files\\Python311\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('click.parser',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('difflib', 'C:\\Program Files\\Python311\\Lib\\difflib.py', 'PYMODULE'),
  ('click.globals',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.formatting',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click.exceptions',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.decorators',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.core',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.shell_completion',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.types',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('uuid', 'C:\\Program Files\\Python311\\Lib\\uuid.py', 'PYMODULE'),
  ('platform', 'C:\\Program Files\\Python311\\Lib\\platform.py', 'PYMODULE'),
  ('uvicorn.supervisors.basereload',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\supervisors\\basereload.py',
   'PYMODULE'),
  ('uvicorn.supervisors',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\supervisors\\__init__.py',
   'PYMODULE'),
  ('uvicorn.server',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\server.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.wsproto_impl',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\protocols\\websockets\\wsproto_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.websockets_impl',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\protocols\\websockets\\websockets_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.auto',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\protocols\\websockets\\auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\protocols\\websockets\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\protocols\\utils.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.httptools_impl',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\protocols\\http\\httptools_impl.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.h11_impl',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\protocols\\http\\h11_impl.py',
   'PYMODULE'),
  ('h11._connection',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._writers',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('h11._util',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._state',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._readers',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._abnf',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._headers',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._events',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._version',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.flow_control',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\protocols\\http\\flow_control.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.auto',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\protocols\\http\\auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.http',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\protocols\\http\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\protocols\\__init__.py',
   'PYMODULE'),
  ('uvicorn.middleware.wsgi',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\middleware\\wsgi.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program Files\\Python311\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program Files\\Python311\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program Files\\Python311\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program Files\\Python311\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Program Files\\Python311\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('uvicorn.middleware.proxy_headers',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\middleware\\proxy_headers.py',
   'PYMODULE'),
  ('uvicorn.middleware.message_logger',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\middleware\\message_logger.py',
   'PYMODULE'),
  ('uvicorn.middleware.asgi2',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\middleware\\asgi2.py',
   'PYMODULE'),
  ('uvicorn.middleware',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\middleware\\__init__.py',
   'PYMODULE'),
  ('uvicorn.loops.uvloop',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\loops\\uvloop.py',
   'PYMODULE'),
  ('uvicorn.loops.auto',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\loops\\auto.py',
   'PYMODULE'),
  ('uvicorn.loops.asyncio',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\loops\\asyncio.py',
   'PYMODULE'),
  ('uvicorn.loops',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\loops\\__init__.py',
   'PYMODULE'),
  ('uvicorn.logging',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\logging.py',
   'PYMODULE'),
  ('uvicorn.lifespan.on',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\lifespan\\on.py',
   'PYMODULE'),
  ('uvicorn.lifespan.off',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\lifespan\\off.py',
   'PYMODULE'),
  ('uvicorn.lifespan',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\lifespan\\__init__.py',
   'PYMODULE'),
  ('uvicorn.importer',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\importer.py',
   'PYMODULE'),
  ('uvicorn._types',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\_types.py',
   'PYMODULE'),
  ('uvicorn._subprocess',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\_subprocess.py',
   'PYMODULE'),
  ('uvicorn.__main__',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\__main__.py',
   'PYMODULE'),
  ('uvicorn.main',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\main.py',
   'PYMODULE'),
  ('uvicorn.config',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\uvicorn\\config.py',
   'PYMODULE'),
  ('dotenv',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.variables',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('dotenv.parser',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('logging.config',
   'C:\\Program Files\\Python311\\Lib\\logging\\config.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Program Files\\Python311\\Lib\\configparser.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Program Files\\Python311\\Lib\\socketserver.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Program Files\\Python311\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('smtplib', 'C:\\Program Files\\Python311\\Lib\\smtplib.py', 'PYMODULE'),
  ('asyncio',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Program Files\\Python311\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python311\\Lib\\_strptime.py', 'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python311\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Program Files\\Python311\\Lib\\pathlib.py', 'PYMODULE'),
  ('tqdm',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\tqdm\\__init__.py',
   'PYMODULE'),
  ('tqdm.notebook',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('html', 'C:\\Program Files\\Python311\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Program Files\\Python311\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('tqdm.version',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm.std',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\tqdm\\std.py',
   'PYMODULE'),
  ('tqdm.utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE'),
  ('tqdm.gui',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\tqdm\\gui.py',
   'PYMODULE'),
  ('tqdm.cli',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\tqdm\\cli.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('requests',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.models',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Program Files\\Python311\\Lib\\__future__.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python311\\Lib\\datetime.py', 'PYMODULE'),
  ('json', 'C:\\Program Files\\Python311\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'C:\\Program Files\\Python311\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Program Files\\Python311\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program Files\\Python311\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('aiofiles',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\aiofiles\\__init__.py',
   'PYMODULE'),
  ('aiofiles.tempfile',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\aiofiles\\tempfile\\__init__.py',
   'PYMODULE'),
  ('aiofiles.tempfile.temptypes',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\aiofiles\\tempfile\\temptypes.py',
   'PYMODULE'),
  ('aiofiles.threadpool.utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\aiofiles\\threadpool\\utils.py',
   'PYMODULE'),
  ('aiofiles.threadpool.text',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\aiofiles\\threadpool\\text.py',
   'PYMODULE'),
  ('aiofiles.threadpool.binary',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\aiofiles\\threadpool\\binary.py',
   'PYMODULE'),
  ('aiofiles.base',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\aiofiles\\base.py',
   'PYMODULE'),
  ('aiofiles.threadpool',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\aiofiles\\threadpool\\__init__.py',
   'PYMODULE'),
  ('pydantic',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\__init__.py',
   'PYMODULE'),
  ('pydantic.root_model',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\root_model.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_repr.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_typing_extra.py',
   'PYMODULE'),
  ('zoneinfo',
   'C:\\Program Files\\Python311\\Lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'C:\\Program Files\\Python311\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'C:\\Program Files\\Python311\\Lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'C:\\Program Files\\Python311\\Lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('sysconfig', 'C:\\Program Files\\Python311\\Lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support',
   'C:\\Program Files\\Python311\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Program Files\\Python311\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_utils.py',
   'PYMODULE'),
  ('pydantic._internal._import_utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_import_utils.py',
   'PYMODULE'),
  ('pydantic._internal._namespace_utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_namespace_utils.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_model_construction.py',
   'PYMODULE'),
  ('pydantic._internal._signature',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_signature.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_core_utils.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_generics.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_generate_schema.py',
   'PYMODULE'),
  ('pydantic._internal._std_types_schema',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_std_types_schema.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._serializers',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_serializers.py',
   'PYMODULE'),
  ('pydantic.v1',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\__init__.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\version.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\types.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\typing.py',
   'PYMODULE'),
  ('pydantic.v1.validators',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\utils.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\tools.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\parse.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\networks.py',
   'PYMODULE'),
  ('pydantic.v1.main',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\main.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\schema.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\json.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\color.py',
   'PYMODULE'),
  ('colorsys', 'C:\\Program Files\\Python311\\Lib\\colorsys.py', 'PYMODULE'),
  ('pydantic.v1.fields',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\fields.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\env_settings.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\decorator.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\config.py',
   'PYMODULE'),
  ('pydantic.v1.class_validators',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\v1\\errors.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_validators.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._docs_extraction',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_docs_extraction.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py',
   'PYMODULE'),
  ('annotated_types',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\annotated_types\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_fields.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_decorators.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_config.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\plugin\\_schema_validator.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\plugin\\_loader.py',
   'PYMODULE'),
  ('pydantic.plugin',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\plugin\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._git',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_internal\\_git.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\deprecated\\tools.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\deprecated\\__init__.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\deprecated\\copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\deprecated\\config.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\deprecated\\class_validators.py',
   'PYMODULE'),
  ('pydantic.warnings',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\warnings.py',
   'PYMODULE'),
  ('pydantic.validate_call_decorator',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\validate_call_decorator.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\type_adapter.py',
   'PYMODULE'),
  ('pydantic.networks',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\networks.py',
   'PYMODULE'),
  ('pydantic.main',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\main.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\deprecated\\json.py',
   'PYMODULE'),
  ('pydantic.color',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\color.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\deprecated\\parse.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\json_schema.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\functional_validators.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\functional_serializers.py',
   'PYMODULE'),
  ('pydantic.fields',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\fields.py',
   'PYMODULE'),
  ('pydantic.errors',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\errors.py',
   'PYMODULE'),
  ('pydantic.config',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\config.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\annotated_handlers.py',
   'PYMODULE'),
  ('pydantic.aliases',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\aliases.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.types',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\types.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic_core\\core_schema.py',
   'PYMODULE'),
  ('pydantic_core',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic_core\\__init__.py',
   'PYMODULE'),
  ('pydantic.version',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\version.py',
   'PYMODULE'),
  ('pydantic._migration',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\_migration.py',
   'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python311\\Lib\\typing.py', 'PYMODULE'),
  ('fastapi.responses',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\responses.py',
   'PYMODULE'),
  ('starlette.responses',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\responses.py',
   'PYMODULE'),
  ('starlette',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\__init__.py',
   'PYMODULE'),
  ('starlette.status',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\status.py',
   'PYMODULE'),
  ('starlette.types',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\types.py',
   'PYMODULE'),
  ('starlette.datastructures',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\datastructures.py',
   'PYMODULE'),
  ('starlette.concurrency',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\concurrency.py',
   'PYMODULE'),
  ('starlette.background',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\background.py',
   'PYMODULE'),
  ('starlette._utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\_utils.py',
   'PYMODULE'),
  ('starlette._compat',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\_compat.py',
   'PYMODULE'),
  ('anyio',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio.streams',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('anyio.abc._eventloop',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\abc\\_eventloop.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('anyio.from_thread',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio._core',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio.abc',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio._core._asyncio_selector_thread',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_core\\_asyncio_selector_thread.py',
   'PYMODULE'),
  ('sniffio',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('anyio._backends',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('fastapi.middleware.cors',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\middleware\\cors.py',
   'PYMODULE'),
  ('fastapi.middleware',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\middleware\\__init__.py',
   'PYMODULE'),
  ('starlette.middleware',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\middleware\\__init__.py',
   'PYMODULE'),
  ('starlette.middleware.cors',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\middleware\\cors.py',
   'PYMODULE'),
  ('fastapi',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\__init__.py',
   'PYMODULE'),
  ('fastapi.websockets',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\websockets.py',
   'PYMODULE'),
  ('starlette.websockets',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\websockets.py',
   'PYMODULE'),
  ('starlette.requests',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\requests.py',
   'PYMODULE'),
  ('starlette.routing',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\routing.py',
   'PYMODULE'),
  ('starlette.convertors',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\convertors.py',
   'PYMODULE'),
  ('multipart.multipart',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\multipart\\multipart.py',
   'PYMODULE'),
  ('multipart',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\multipart\\__init__.py',
   'PYMODULE'),
  ('python_multipart',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\python_multipart\\__init__.py',
   'PYMODULE'),
  ('python_multipart.multipart',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\python_multipart\\multipart.py',
   'PYMODULE'),
  ('python_multipart.exceptions',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\python_multipart\\exceptions.py',
   'PYMODULE'),
  ('python_multipart.decoders',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\python_multipart\\decoders.py',
   'PYMODULE'),
  ('starlette.formparsers',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\formparsers.py',
   'PYMODULE'),
  ('starlette.exceptions',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\exceptions.py',
   'PYMODULE'),
  ('starlette.middleware.exceptions',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\middleware\\exceptions.py',
   'PYMODULE'),
  ('fastapi.requests',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\requests.py',
   'PYMODULE'),
  ('fastapi.param_functions',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\param_functions.py',
   'PYMODULE'),
  ('fastapi._compat',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\_compat.py',
   'PYMODULE'),
  ('pydantic.utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\utils.py',
   'PYMODULE'),
  ('pydantic.typing',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\typing.py',
   'PYMODULE'),
  ('pydantic.schema',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\schema.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic\\class_validators.py',
   'PYMODULE'),
  ('fastapi.openapi.constants',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\openapi\\constants.py',
   'PYMODULE'),
  ('fastapi.openapi',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\openapi\\__init__.py',
   'PYMODULE'),
  ('fastapi.types',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\types.py',
   'PYMODULE'),
  ('fastapi.exceptions',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\exceptions.py',
   'PYMODULE'),
  ('fastapi.datastructures',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\datastructures.py',
   'PYMODULE'),
  ('fastapi.background',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\background.py',
   'PYMODULE'),
  ('fastapi.applications',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\applications.py',
   'PYMODULE'),
  ('starlette.middleware.errors',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\middleware\\errors.py',
   'PYMODULE'),
  ('starlette.middleware.base',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\middleware\\base.py',
   'PYMODULE'),
  ('starlette.applications',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\starlette\\applications.py',
   'PYMODULE'),
  ('fastapi.utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\utils.py',
   'PYMODULE'),
  ('fastapi.openapi.utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\openapi\\utils.py',
   'PYMODULE'),
  ('fastapi.openapi.models',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\openapi\\models.py',
   'PYMODULE'),
  ('fastapi.encoders',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\encoders.py',
   'PYMODULE'),
  ('fastapi.dependencies.utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\dependencies\\utils.py',
   'PYMODULE'),
  ('fastapi.dependencies',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\dependencies\\__init__.py',
   'PYMODULE'),
  ('fastapi.security.open_id_connect_url',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\security\\open_id_connect_url.py',
   'PYMODULE'),
  ('fastapi.security',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\security\\__init__.py',
   'PYMODULE'),
  ('fastapi.security.http',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\security\\http.py',
   'PYMODULE'),
  ('fastapi.security.utils',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\security\\utils.py',
   'PYMODULE'),
  ('fastapi.security.api_key',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\security\\api_key.py',
   'PYMODULE'),
  ('fastapi.security.oauth2',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\security\\oauth2.py',
   'PYMODULE'),
  ('fastapi.security.base',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\security\\base.py',
   'PYMODULE'),
  ('fastapi.concurrency',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\concurrency.py',
   'PYMODULE'),
  ('fastapi.dependencies.models',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\dependencies\\models.py',
   'PYMODULE'),
  ('fastapi.openapi.docs',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\openapi\\docs.py',
   'PYMODULE'),
  ('fastapi.middleware.asyncexitstack',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\middleware\\asyncexitstack.py',
   'PYMODULE'),
  ('fastapi.logger',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\logger.py',
   'PYMODULE'),
  ('fastapi.exception_handlers',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\exception_handlers.py',
   'PYMODULE'),
  ('fastapi.routing',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\routing.py',
   'PYMODULE'),
  ('fastapi.params',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\fastapi\\params.py',
   'PYMODULE')],
 [('python311.dll', 'C:\\Program Files\\Python311\\python311.dll', 'BINARY'),
  ('select.pyd', 'C:\\Program Files\\Python311\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program Files\\Python311\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'C:\\Program Files\\Python311\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'C:\\Program Files\\Python311\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python311\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python311\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'C:\\Program Files\\Python311\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_zoneinfo.pyd',
   'C:\\Program Files\\Python311\\DLLs\\_zoneinfo.pyd',
   'EXTENSION'),
  ('pydantic_core\\_pydantic_core.cp311-win_amd64.pyd',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\pydantic_core\\_pydantic_core.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program Files\\Python311\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Program Files\\Python311\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program Files\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\Windows\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('certifi\\py.typed',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'F:\\AIGC\\Project\\LeadToMd\\venv\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('base_library.zip',
   'F:\\AIGC\\Project\\LeadToMd\\build\\LeadToMd\\base_library.zip',
   'DATA')])
