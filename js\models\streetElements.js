/**
 * 街道元素系统 - 包含路灯、树木、长椅等街道装饰物
 */
class StreetElements {
    constructor(scene, roadSystem) {
        this.scene = scene;
        this.roadSystem = roadSystem;
        this.elements = {
            streetLights: [],
            trees: [],
            benches: [],
            trashBins: []
        };
        this.materials = this.createMaterials();
    }
    
    createMaterials() {
        return {
            // 路灯材质
            lampPost: new THREE.MeshPhongMaterial({
                color: 0x333333,
                specular: 0x111111,
                shininess: 30
            }),
            lampGlass: new THREE.MeshPhongMaterial({
                color: 0xFFFFCC,
                specular: 0xFFFFFF,
                shininess: 100,
                transparent: true,
                opacity: 0.7
            }),
            
            // 树木材质
            trunk: new THREE.MeshPhongMaterial({
                color: 0x8B4513,
                specular: 0x332211,
                shininess: 10
            }),
            leaves: new THREE.MeshPhongMaterial({
                color: 0x228B22,
                specular: 0x003300,
                shininess: 5
            }),
            
            // 长椅材质
            benchWood: new THREE.MeshPhongMaterial({
                color: 0x8B4513,
                specular: 0x332211,
                shininess: 30
            }),
            benchMetal: new THREE.MeshPhongMaterial({
                color: 0x444444,
                specular: 0x111111,
                shininess: 80
            }),
            
            // 垃圾桶材质
            trashBin: new THREE.MeshPhongMaterial({
                color: 0x444444,
                specular: 0x111111,
                shininess: 20
            })
        };
    }
    
    // 生成街道元素
    generateElements() {
        if (!this.roadSystem || !this.roadSystem.roads || this.roadSystem.roads.length === 0) {
            console.error("无法生成街道元素：道路系统未初始化");
            return;
        }
        
        // 生成路灯
        this.generateStreetLights();
        
        // 生成树木
        this.generateTrees();
        
        // 生成长椅和垃圾桶
        this.generateStreetFurniture();
    }
    
    // 生成路灯
    generateStreetLights() {
        // 在道路两侧放置路灯
        this.roadSystem.roads.forEach(road => {
            // 获取道路信息
            const roadBbox = new THREE.Box3().setFromObject(road);
            const roadWidth = road.geometry.parameters.width;
            const roadLength = road.geometry.parameters.height;
            const lampSpacing = 30; // 每隔30个单位放置一个路灯
            
            // 确定灯的数量
            const lampsPerSide = Math.floor(roadLength / lampSpacing);
            
            // 路灯位置的偏移量
            const offset = roadWidth / 2 + 2; // 距离道路边缘2个单位
            
            // 生成路灯
            for (let i = 0; i < lampsPerSide; i++) {
                // 计算位置
                const t = (i + 0.5) / lampsPerSide; // 0到1之间的值
                const x = road.position.x + road.geometry.parameters.width/2 * Math.sin(road.rotation.z);
                const z = road.position.z - road.geometry.parameters.width/2 * Math.cos(road.rotation.z);
                const y = 0;
                
                // 创建路灯
                const lampRight = this.createStreetLight();
                const lampLeft = this.createStreetLight();
                
                // 设置路灯位置
                lampRight.position.set(x + offset * Math.cos(road.rotation.z), y, z + offset * Math.sin(road.rotation.z));
                lampLeft.position.set(x - offset * Math.cos(road.rotation.z), y, z - offset * Math.sin(road.rotation.z));
                
                // 应用一定的随机旋转
                lampRight.rotation.y = road.rotation.z + Math.PI/2 + (Math.random() - 0.5) * 0.2;
                lampLeft.rotation.y = road.rotation.z - Math.PI/2 + (Math.random() - 0.5) * 0.2;
                
                // 添加到场景
                this.scene.add(lampRight);
                this.scene.add(lampLeft);
                
                // 保存引用
                this.elements.streetLights.push(lampRight);
                this.elements.streetLights.push(lampLeft);
            }
        });
    }
    
    // 创建单个路灯
    createStreetLight() {
        const lampGroup = new THREE.Group();
        
        // 灯柱
        const postHeight = 8;
        const postGeometry = new THREE.CylinderGeometry(0.2, 0.3, postHeight, 8);
        const post = new THREE.Mesh(postGeometry, this.materials.lampPost);
        post.position.set(0, postHeight/2, 0);
        post.castShadow = true;
        lampGroup.add(post);
        
        // 灯臂
        const armLength = 2;
        const armGeometry = new THREE.CylinderGeometry(0.1, 0.1, armLength, 8);
        const arm = new THREE.Mesh(armGeometry, this.materials.lampPost);
        arm.position.set(0, postHeight - 0.5, armLength/2);
        arm.rotation.x = Math.PI/2;
        arm.castShadow = true;
        lampGroup.add(arm);
        
        // 灯罩
        const lampShadeGeometry = new THREE.SphereGeometry(0.5, 16, 8, 0, Math.PI * 2, 0, Math.PI / 2);
        const lampShade = new THREE.Mesh(lampShadeGeometry, this.materials.lampPost);
        lampShade.position.set(0, postHeight - 0.5, armLength);
        lampShade.rotation.x = Math.PI;
        lampShade.castShadow = true;
        lampGroup.add(lampShade);
        
        // 灯泡（点光源）
        const lightBulb = new THREE.PointLight(0xFFFFCC, 1, 20);
        lightBulb.position.set(0, postHeight - 0.7, armLength);
        lightBulb.castShadow = true;
        lightBulb.shadow.mapSize.width = 512;
        lightBulb.shadow.mapSize.height = 512;
        lightBulb.visible = false; // 初始状态灯是关的
        lampGroup.add(lightBulb);
        
        // 灯泡的可见部分
        const bulbGeometry = new THREE.SphereGeometry(0.2, 8, 8);
        const bulb = new THREE.Mesh(bulbGeometry, this.materials.lampGlass);
        bulb.position.set(0, postHeight - 0.7, armLength);
        lampGroup.add(bulb);
        
        // 保存光源引用
        lampGroup.userData.light = lightBulb;
        lampGroup.userData.bulb = bulb;
        
        return lampGroup;
    }
    
    // 生成树木
    generateTrees() {
        // 在城市的随机位置生成树木，避开道路
        const citySize = 200;
        const treeCount = 100;
        
        for (let i = 0; i < treeCount; i++) {
            // 随机位置
            const x = (Math.random() - 0.5) * citySize;
            const z = (Math.random() - 0.5) * citySize;
            
            // 检查是否在道路上
            let isOnRoad = false;
            for (const road of this.roadSystem.roads) {
                const roadBbox = new THREE.Box3().setFromObject(road);
                roadBbox.expandByScalar(5); // 扩大检测范围
                
                const roadPosition = new THREE.Vector3(x, 0, z);
                if (roadBbox.containsPoint(roadPosition)) {
                    isOnRoad = true;
                    break;
                }
            }
            
            // 如果不在道路上，创建树
            if (!isOnRoad) {
                const tree = this.createTree();
                tree.position.set(x, 0, z);
                tree.rotation.y = Math.random() * Math.PI * 2; // 随机旋转
                this.scene.add(tree);
                this.elements.trees.push(tree);
            }
        }
    }
    
    // 创建单棵树
    createTree() {
        const treeGroup = new THREE.Group();
        
        // 随机选择树的类型
        const treeType = Math.floor(Math.random() * 3);
        
        // 根据类型创建不同的树
        switch (treeType) {
            case 0: // 标准树
                // 树干
                const trunkHeight = 2 + Math.random() * 3;
                const trunkRadius = 0.3 + Math.random() * 0.2;
                const trunkGeometry = new THREE.CylinderGeometry(trunkRadius * 0.7, trunkRadius, trunkHeight, 8);
                const trunk = new THREE.Mesh(trunkGeometry, this.materials.trunk);
                trunk.position.set(0, trunkHeight/2, 0);
                trunk.castShadow = true;
                treeGroup.add(trunk);
                
                // 树冠
                const leavesRadius = 2 + Math.random() * 2;
                const leavesGeometry = new THREE.SphereGeometry(leavesRadius, 8, 8);
                const leaves = new THREE.Mesh(leavesGeometry, this.materials.leaves);
                leaves.position.set(0, trunkHeight + leavesRadius * 0.7, 0);
                leaves.castShadow = true;
                treeGroup.add(leaves);
                break;
                
            case 1: // 松树
                // 树干
                const pineHeight = 5 + Math.random() * 5;
                const pineRadius = 0.4 + Math.random() * 0.3;
                const pineGeometry = new THREE.CylinderGeometry(pineRadius * 0.5, pineRadius, pineHeight, 8);
                const pine = new THREE.Mesh(pineGeometry, this.materials.trunk);
                pine.position.set(0, pineHeight/2, 0);
                pine.castShadow = true;
                treeGroup.add(pine);
                
                // 松树层
                const layers = 3 + Math.floor(Math.random() * 3);
                for (let i = 0; i < layers; i++) {
                    const layerSize = 1.5 + (layers - i) * 0.7;
                    const layerHeight = 1 + Math.random() * 0.5;
                    const coneGeometry = new THREE.ConeGeometry(layerSize, layerHeight, 8);
                    const cone = new THREE.Mesh(coneGeometry, this.materials.leaves);
                    cone.position.set(0, pineHeight - (layers - i) * layerHeight * 0.6, 0);
                    cone.castShadow = true;
                    treeGroup.add(cone);
                }
                break;
                
            case 2: // 低矮灌木
                // 灌木丛
                const bushSize = 1 + Math.random() * 1.5;
                const bushGeometry = new THREE.DodecahedronGeometry(bushSize, 1);
                const bush = new THREE.Mesh(bushGeometry, this.materials.leaves);
                bush.position.set(0, bushSize, 0);
                bush.castShadow = true;
                treeGroup.add(bush);
                break;
        }
        
        return treeGroup;
    }
    
    // 生成街道家具（长椅和垃圾桶）
    generateStreetFurniture() {
        // 随机在城市中放置长椅和垃圾桶
        const citySize = 200;
        const furnitureCount = 50; // 总数量
        
        for (let i = 0; i < furnitureCount; i++) {
            // 随机位置
            const x = (Math.random() - 0.5) * citySize;
            const z = (Math.random() - 0.5) * citySize;
            
            // 检查是否在道路上
            let isOnRoad = false;
            for (const road of this.roadSystem.roads) {
                const roadBbox = new THREE.Box3().setFromObject(road);
                roadBbox.expandByScalar(2); // 稍微扩大检测范围
                
                const position = new THREE.Vector3(x, 0, z);
                if (roadBbox.containsPoint(position)) {
                    isOnRoad = true;
                    break;
                }
            }
            
            // 如果不在道路上
            if (!isOnRoad) {
                // 50%的概率生成长椅，50%的概率生成垃圾桶
                if (Math.random() < 0.5) {
                    const bench = this.createBench();
                    bench.position.set(x, 0, z);
                    bench.rotation.y = Math.random() * Math.PI * 2; // 随机旋转
                    this.scene.add(bench);
                    this.elements.benches.push(bench);
                } else {
                    const trashBin = this.createTrashBin();
                    trashBin.position.set(x, 0, z);
                    this.scene.add(trashBin);
                    this.elements.trashBins.push(trashBin);
                }
            }
        }
    }
    
    // 创建长椅
    createBench() {
        const benchGroup = new THREE.Group();
        
        // 座椅
        const seatWidth = 4;
        const seatHeight = 0.1;
        const seatDepth = 1.2;
        const seatGeometry = new THREE.BoxGeometry(seatWidth, seatHeight, seatDepth);
        const seat = new THREE.Mesh(seatGeometry, this.materials.benchWood);
        seat.position.set(0, 0.6, 0);
        seat.castShadow = true;
        benchGroup.add(seat);
        
        // 靠背
        const backHeight = 1;
        const backGeometry = new THREE.BoxGeometry(seatWidth, backHeight, seatHeight);
        const back = new THREE.Mesh(backGeometry, this.materials.benchWood);
        back.position.set(0, 0.6 + backHeight/2, -seatDepth/2 + seatHeight/2);
        back.castShadow = true;
        benchGroup.add(back);
        
        // 腿（左前）
        const legGeometry = new THREE.BoxGeometry(0.1, 0.6, 0.1);
        const legLF = new THREE.Mesh(legGeometry, this.materials.benchMetal);
        legLF.position.set(-seatWidth/2 + 0.2, 0.3, seatDepth/2 - 0.2);
        legLF.castShadow = true;
        benchGroup.add(legLF);
        
        // 腿（右前）
        const legRF = new THREE.Mesh(legGeometry, this.materials.benchMetal);
        legRF.position.set(seatWidth/2 - 0.2, 0.3, seatDepth/2 - 0.2);
        legRF.castShadow = true;
        benchGroup.add(legRF);
        
        // 腿（左后）
        const legLB = new THREE.Mesh(legGeometry, this.materials.benchMetal);
        legLB.position.set(-seatWidth/2 + 0.2, 0.3, -seatDepth/2 + 0.2);
        legLB.castShadow = true;
        benchGroup.add(legLB);
        
        // 腿（右后）
        const legRB = new THREE.Mesh(legGeometry, this.materials.benchMetal);
        legRB.position.set(seatWidth/2 - 0.2, 0.3, -seatDepth/2 + 0.2);
        legRB.castShadow = true;
        benchGroup.add(legRB);
        
        return benchGroup;
    }
    
    // 创建垃圾桶
    createTrashBin() {
        const binGroup = new THREE.Group();
        
        // 垃圾桶主体
        const binHeight = 1.2;
        const binRadius = 0.4;
        const binGeometry = new THREE.CylinderGeometry(binRadius, binRadius * 0.8, binHeight, 8);
        const bin = new THREE.Mesh(binGeometry, this.materials.trashBin);
        bin.position.set(0, binHeight/2, 0);
        bin.castShadow = true;
        binGroup.add(bin);
        
        return binGroup;
    }
    
    // 更新街道元素（主要是路灯）
    updateStreetLights(currentTime) {
        // 判断是否为夜间 (18:00 - 6:00)
        const isNight = currentTime > 18 || currentTime < 6;
        
        // 更新所有路灯
        this.elements.streetLights.forEach(lamp => {
            if (lamp.userData.light) {
                lamp.userData.light.visible = isNight;
                
                if (isNight) {
                    // 夜间：灯亮
                    if (lamp.userData.bulb) {
                        lamp.userData.bulb.material = this.materials.lampGlass.clone();
                        lamp.userData.bulb.material.emissive = new THREE.Color(0xffffcc);
                        lamp.userData.bulb.material.emissiveIntensity = 0.8;
                    }
                } else {
                    // 白天：灯灭
                    if (lamp.userData.bulb) {
                        lamp.userData.bulb.material = this.materials.lampGlass.clone();
                        lamp.userData.bulb.material.emissiveIntensity = 0;
                    }
                }
            }
        });
    }
} 