/**
 * 建筑物系统 - 包含多种类型的建筑物
 */
class BuildingSystem {
    constructor(scene) {
        this.scene = scene;
        this.buildings = [];
        this.materials = this.createMaterials();
    }

    createMaterials() {
        return {
            // 摩天大楼材质
            skyscraper: [
                new THREE.MeshPhongMaterial({
                    color: 0x4682B4, // 钢蓝色
                    specular: 0xCCCCCC,
                    shininess: 100
                }),
                new THREE.MeshPhongMaterial({
                    color: 0x1E1E1E, // 深黑色
                    specular: 0x444444,
                    shininess: 80
                }),
                new THREE.MeshPhongMaterial({
                    color: 0x708090, // 灰蓝色
                    specular: 0x999999,
                    shininess: 90
                })
            ],
            
            // 办公楼材质
            office: [
                new THREE.MeshPhongMaterial({
                    color: 0xA9A9A9, // 深灰色
                    specular: 0x777777,
                    shininess: 30
                }),
                new THREE.MeshPhongMaterial({
                    color: 0xD3D3D3, // 浅灰色
                    specular: 0xAAAAAA,
                    shininess: 50
                })
            ],
            
            // 公寓楼材质
            apartment: [
                new THREE.MeshPhongMaterial({
                    color: 0xCD853F, // 棕色
                    specular: 0x553322,
                    shininess: 20
                }),
                new THREE.MeshPhongMaterial({
                    color: 0xDEB887, // 棕黄色
                    specular: 0x664433,
                    shininess: 25
                })
            ],
            
            // 商店材质
            shop: [
                new THREE.MeshPhongMaterial({
                    color: 0x98FB98, // 浅绿色
                    specular: 0x559955,
                    shininess: 40
                }),
                new THREE.MeshPhongMaterial({
                    color: 0x87CEFA, // 浅蓝色
                    specular: 0x5588AA,
                    shininess: 40
                }),
                new THREE.MeshPhongMaterial({
                    color: 0xFFA07A, // 浅橙色
                    specular: 0xAA7755,
                    shininess: 40
                })
            ],
            
            // 窗户材质
            window: new THREE.MeshPhongMaterial({
                color: 0x404040,
                specular: 0xFFFFFF,
                shininess: 100,
                transparent: true,
                opacity: 0.8
            }),
            
            // 发光窗户（夜间使用）
            glowingWindow: new THREE.MeshPhongMaterial({
                color: 0xFFFF99,
                specular: 0xFFFFFF,
                shininess: 100,
                transparent: true,
                opacity: 0.9,
                emissive: 0xFFFF77,
                emissiveIntensity: 0.5
            })
        };
    }

    // 创建摩天大楼
    createSkyscraper(x, z) {
        const height = 80 + Math.random() * 100;
        const width = 20 + Math.random() * 10;
        const depth = 20 + Math.random() * 10;
        
        // 随机选择材质
        const materialIndex = Math.floor(Math.random() * this.materials.skyscraper.length);
        const material = this.materials.skyscraper[materialIndex];
        
        const building = this.createBuildingWithWindows(x, z, height, width, depth, material, height / 10);
        
        // 添加顶部结构
        if (Math.random() > 0.5) {
            const topHeight = height * 0.15;
            const topWidth = width * 0.5;
            const topDepth = depth * 0.5;
            
            const topGeometry = new THREE.BoxGeometry(topWidth, topHeight, topDepth);
            const top = new THREE.Mesh(topGeometry, material);
            top.position.set(0, height/2 + topHeight/2, 0);
            top.castShadow = true;
            building.add(top);
            
            // 天线
            if (Math.random() > 0.5) {
                const antennaHeight = height * 0.1;
                const antennaGeometry = new THREE.CylinderGeometry(0.5, 1, antennaHeight, 8);
                const antenna = new THREE.Mesh(antennaGeometry, material);
                antenna.position.set(0, height/2 + topHeight + antennaHeight/2, 0);
                antenna.castShadow = true;
                building.add(antenna);
            }
        }
        
        return building;
    }
    
    // 创建办公楼
    createOfficeBuilding(x, z) {
        const height = 40 + Math.random() * 40;
        const width = 30 + Math.random() * 15;
        const depth = 20 + Math.random() * 10;
        
        // 随机选择材质
        const materialIndex = Math.floor(Math.random() * this.materials.office.length);
        const material = this.materials.office[materialIndex];
        
        const building = this.createBuildingWithWindows(x, z, height, width, depth, material, height / 8);
        
        return building;
    }
    
    // 创建公寓楼
    createApartment(x, z) {
        const height = 20 + Math.random() * 20;
        const width = 15 + Math.random() * 10;
        const depth = 15 + Math.random() * 10;
        
        // 随机选择材质
        const materialIndex = Math.floor(Math.random() * this.materials.apartment.length);
        const material = this.materials.apartment[materialIndex];
        
        const building = this.createBuildingWithWindows(x, z, height, width, depth, material, 5);
        
        // 添加阳台
        if (Math.random() > 0.5) {
            this.addBalconies(building, width, depth, height);
        }
        
        return building;
    }
    
    // 创建商店
    createShop(x, z) {
        const height = 5 + Math.random() * 10;
        const width = 10 + Math.random() * 20;
        const depth = 10 + Math.random() * 15;
        
        // 随机选择材质
        const materialIndex = Math.floor(Math.random() * this.materials.shop.length);
        const material = this.materials.shop[materialIndex];
        
        // 创建主体
        const geometry = new THREE.BoxGeometry(width, height, depth);
        const building = new THREE.Mesh(geometry, material);
        building.position.set(x, height/2, z);
        building.castShadow = true;
        building.receiveShadow = true;
        
        // 添加门
        const doorWidth = 4;
        const doorHeight = Math.min(height * 0.8, 4);
        const doorGeometry = new THREE.PlaneGeometry(doorWidth, doorHeight);
        const doorMaterial = new THREE.MeshPhongMaterial({
            color: 0x8B4513,
            specular: 0xAA8866,
            shininess: 50
        });
        const door = new THREE.Mesh(doorGeometry, doorMaterial);
        door.position.set(0, -height/2 + doorHeight/2, depth/2 + 0.1);
        building.add(door);
        
        // 添加招牌
        const signWidth = width * 0.8;
        const signHeight = 2;
        const signGeometry = new THREE.BoxGeometry(signWidth, signHeight, 0.5);
        const signMaterial = new THREE.MeshPhongMaterial({
            color: Math.random() * 0xFFFFFF,
            specular: 0xFFFFFF,
            shininess: 100,
            emissive: Math.random() * 0xFFFFFF,
            emissiveIntensity: 0.5
        });
        const sign = new THREE.Mesh(signGeometry, signMaterial);
        sign.position.set(0, height/2 + signHeight/2, depth/2 + 0.5);
        building.add(sign);
        
        this.scene.add(building);
        this.buildings.push(building);
        
        return building;
    }
    
    // 通用方法：创建带窗户的建筑物
    createBuildingWithWindows(x, z, height, width, depth, material, windowSpacing) {
        // 创建主体
        const geometry = new THREE.BoxGeometry(width, height, depth);
        const building = new THREE.Mesh(geometry, material);
        building.position.set(x, height/2, z);
        building.castShadow = true;
        building.receiveShadow = true;
        
        // 保存建筑物的时间特征
        building.userData = {
            windows: [],
            windowLightChance: Math.random() * 0.3 + 0.3, // 30%-60%的窗户会亮
            flickerSpeed: Math.random() * 0.01 + 0.005    // 闪烁速度
        };
        
        // 添加窗户
        this.addWindows(building, width, height, depth, windowSpacing);
        
        this.scene.add(building);
        this.buildings.push(building);
        
        return building;
    }
    
    // 添加窗户到建筑物
    addWindows(building, width, height, depth, spacing) {
        const windowSize = 2;
        const windowGeometry = new THREE.PlaneGeometry(windowSize, windowSize);
        const windowMaterial = this.materials.window.clone();
        const windows = [];
        
        // 前面窗户
        for (let y = spacing/2; y < height - spacing/2; y += spacing) {
            for (let x = -width/2 + spacing; x <= width/2 - spacing; x += spacing) {
                if (Math.random() > 0.2) { // 80%的概率有窗户
                    const window = new THREE.Mesh(windowGeometry, windowMaterial.clone());
                    window.position.set(x, y - height/2, depth/2 + 0.1);
                    building.add(window);
                    windows.push(window);
                }
            }
        }
        
        // 后面窗户
        for (let y = spacing/2; y < height - spacing/2; y += spacing) {
            for (let x = -width/2 + spacing; x <= width/2 - spacing; x += spacing) {
                if (Math.random() > 0.2) {
                    const window = new THREE.Mesh(windowGeometry, windowMaterial.clone());
                    window.position.set(x, y - height/2, -depth/2 - 0.1);
                    window.rotation.y = Math.PI;
                    building.add(window);
                    windows.push(window);
                }
            }
        }
        
        // 左侧窗户
        for (let y = spacing/2; y < height - spacing/2; y += spacing) {
            for (let z = -depth/2 + spacing; z <= depth/2 - spacing; z += spacing) {
                if (Math.random() > 0.2) {
                    const window = new THREE.Mesh(windowGeometry, windowMaterial.clone());
                    window.position.set(-width/2 - 0.1, y - height/2, z);
                    window.rotation.y = -Math.PI / 2;
                    building.add(window);
                    windows.push(window);
                }
            }
        }
        
        // 右侧窗户
        for (let y = spacing/2; y < height - spacing/2; y += spacing) {
            for (let z = -depth/2 + spacing; z <= depth/2 - spacing; z += spacing) {
                if (Math.random() > 0.2) {
                    const window = new THREE.Mesh(windowGeometry, windowMaterial.clone());
                    window.position.set(width/2 + 0.1, y - height/2, z);
                    window.rotation.y = Math.PI / 2;
                    building.add(window);
                    windows.push(window);
                }
            }
        }
        
        building.userData.windows = windows;
    }
    
    // 添加阳台到建筑物
    addBalconies(building, width, depth, height) {
        const balconyCount = 2 + Math.floor(Math.random() * 3); // 2-4个阳台/层
        const balconyFloors = Math.floor(height / 6); // 每6个单位高度一层
        
        for (let floor = 1; floor < balconyFloors; floor++) {
            for (let i = 0; i < balconyCount; i++) {
                // 阳台尺寸
                const balconyWidth = 4;
                const balconyDepth = 2;
                const balconyHeight = 1;
                
                // 阳台材质
                const balconyMaterial = building.material;
                
                // 创建阳台
                const balconyGeometry = new THREE.BoxGeometry(balconyWidth, balconyHeight, balconyDepth);
                const balcony = new THREE.Mesh(balconyGeometry, balconyMaterial);
                
                // 随机选择阳台位置
                let posX, posZ, rotY;
                const side = Math.floor(Math.random() * 4); // 0:前, 1:右, 2:后, 3:左
                
                switch(side) {
                    case 0: // 前
                        posX = (Math.random() - 0.5) * (width - balconyWidth);
                        posZ = depth/2 + balconyDepth/2;
                        rotY = 0;
                        break;
                    case 1: // 右
                        posX = width/2 + balconyDepth/2;
                        posZ = (Math.random() - 0.5) * (depth - balconyWidth);
                        rotY = Math.PI/2;
                        break;
                    case 2: // 后
                        posX = (Math.random() - 0.5) * (width - balconyWidth);
                        posZ = -depth/2 - balconyDepth/2;
                        rotY = Math.PI;
                        break;
                    case 3: // 左
                        posX = -width/2 - balconyDepth/2;
                        posZ = (Math.random() - 0.5) * (depth - balconyWidth);
                        rotY = -Math.PI/2;
                        break;
                }
                
                balcony.position.set(posX, -height/2 + floor * 6, posZ);
                balcony.rotation.y = rotY;
                
                // 添加栏杆
                const railHeight = 1;
                const railGeometry = new THREE.BoxGeometry(balconyWidth, railHeight, 0.2);
                const rail = new THREE.Mesh(railGeometry, balconyMaterial);
                rail.position.set(0, balconyHeight/2 + railHeight/2, balconyDepth/2 - 0.1);
                balcony.add(rail);
                
                // 添加到建筑物
                building.add(balcony);
            }
        }
    }

    // 生成整个城市
    generateCity(buildingCount = 40) {
        console.log("生成城市建筑，数量:", buildingCount);
        
        const citySize = 200;
        
        // 生成不同类型的建筑物
        for (let i = 0; i < buildingCount; i++) {
            const x = (Math.random() - 0.5) * citySize;
            const z = (Math.random() - 0.5) * citySize;
            const distanceFromCenter = Math.sqrt(x*x + z*z);
            
            // 根据到中心的距离决定建筑类型
            if (distanceFromCenter < citySize * 0.2) {
                // 市中心 - 摩天大楼
                this.createSkyscraper(x, z);
            }
            else if (distanceFromCenter < citySize * 0.4) {
                // 近市中心 - 办公楼
                this.createOfficeBuilding(x, z);
            }
            else if (distanceFromCenter < citySize * 0.7) {
                // 中等距离 - 公寓楼
                this.createApartment(x, z);
            }
            else {
                // 远离市中心 - 商店
                this.createShop(x, z);
            }
        }
        
        console.log("城市建筑生成完成");
    }

    // 更新建筑物窗户（根据时间）
    update(time, currentTime) {
        // 判断是否为夜间
        const isNight = currentTime === undefined ? 
            (time % 24 > 18 || time % 24 < 6) : 
            (currentTime > 18 || currentTime < 6);
            
        // 更新所有建筑物的窗户
        this.buildings.forEach(building => {
            if (building.userData && building.userData.windows) {
                const windows = building.userData.windows;
                const windowLightChance = building.userData.windowLightChance;
                const flickerSpeed = building.userData.flickerSpeed;
                
                windows.forEach(window => {
                    if (isNight) {
                        // 夜间：随机窗户发光
                        if (Math.random() < flickerSpeed) {
                            // 随机决定窗户是亮还是暗
                            if (Math.random() < windowLightChance) {
                                // 窗户亮
                                window.material = this.materials.glowingWindow.clone();
                                // 随机亮度
                                window.material.emissiveIntensity = 0.3 + Math.random() * 0.7;
                            } else {
                                // 窗户暗
                                window.material = this.materials.window.clone();
                            }
                        }
                    } else {
                        // 白天：窗户不发光
                        if (window.material.emissiveIntensity > 0) {
                            window.material = this.materials.window.clone();
                        }
                    }
                });
            }
        });
    }
} 